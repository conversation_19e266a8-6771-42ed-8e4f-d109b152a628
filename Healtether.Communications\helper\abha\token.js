import { existsSync, readFileSync, writeFileSync } from "fs";
import { join } from "path";

const PUBLIC_TOKEN_FILE_PATH = join(process.cwd(), "public_token.json");

export const publicToken = async () => {
  let tokenData = { token: null, expiresAt: null };

  if (existsSync(PUBLIC_TOKEN_FILE_PATH)) {
    const fileData = readFileSync(PUBLIC_TOKEN_FILE_PATH, "utf8");
    tokenData = JSON.parse(fileData);
    console.log(tokenData, "tokenData");
  }

  if (tokenData.token && new Date(tokenData.expiresAt) > new Date()) {
    return tokenData.token;
  }

  const data = {
    clientId: "SBX_003515",
    clientSecret: "85bb52e3-1a49-4227-b630-8e93dcd9e8bd",
    grantType: "client_credentials",
  };

  const result = await fetch("https://dev.abdm.gov.in/gateway/v0.5/sessions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!result.ok) {
    throw new Error(`Failed to fetch token. Status: ${result.status}`);
  }

  const response = await result.json();
  const newToken = response?.accessToken?.toString();
  const expiresIn = response?.expiresIn || 0;
  const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString(); // UTC
  tokenData = { token: newToken, expiresAt: expiresAt };
  writeFileSync(PUBLIC_TOKEN_FILE_PATH, JSON.stringify(tokenData), "utf8");

  return newToken;
};
