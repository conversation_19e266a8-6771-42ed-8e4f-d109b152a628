#!/usr/bin/env node

// Emergency MongoDB Cleanup Script
// This script quickly deletes old FHIR records to free up space

const mongoose = require('mongoose');
const dotenv = require('dotenv');

dotenv.config();

const MONGO_URI = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/healtether';

async function emergencyCleanup() {
  console.log('🚨 EMERGENCY MongoDB Cleanup');
  console.log('============================');

  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected successfully');

    const db = mongoose.connection.db;

    // Show current size
    const stats = await db.stats();
    console.log(`\nCurrent database size: ${(stats.dataSize / (1024 * 1024)).toFixed(2)} MB`);

    // FHIR collections to clean
    const collections = [
      'prescriptionfhirrecords',
      'healthdocumentfhirrecords',
      'opconsultfhirrecords',
      'immunizationreportfhirrecords',
      'diagnosticreportfhirrecords',
      'dischargesummaryfhirrecords',
      'wellnessreportfhirrecords',
      'invoicereportfhirrecords'
    ];

    let totalDeleted = 0;

    // Delete records older than 7 days (aggressive cleanup)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    console.log(`\nDeleting records older than: ${sevenDaysAgo.toISOString()}`);

    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);

        // Try multiple date field patterns
        const queries = [
          { "created.on": { $lt: sevenDaysAgo } },
          { "createdAt": { $lt: sevenDaysAgo } },
          { "created": { $lt: sevenDaysAgo } },
          { "_id": { $lt: mongoose.Types.ObjectId.createFromTime(sevenDaysAgo.getTime() / 1000) } }
        ];

        for (const query of queries) {
          const count = await collection.countDocuments(query);
          if (count > 0) {
            console.log(`\n${collectionName}: Deleting ${count} old records...`);
            const result = await collection.deleteMany(query);
            console.log(`✅ Deleted ${result.deletedCount} records`);
            totalDeleted += result.deletedCount;
            break;
          }
        }
      } catch (error) {
        console.log(`⚠️  Error with ${collectionName}: ${error.message}`);
      }
    }

    // If still not enough space, delete more aggressively
    const newStats = await db.stats();
    const currentSizeMB = newStats.dataSize / (1024 * 1024);

    if (currentSizeMB > 500) { // Still over 500MB
      console.log(`\n⚠️  Still using ${currentSizeMB.toFixed(2)} MB. Deleting ALL old FHIR records...`);

      for (const collectionName of collections) {
        try {
          const collection = db.collection(collectionName);
          const count = await collection.countDocuments();

          if (count > 0) {
            console.log(`${collectionName}: Deleting ALL ${count} records...`);
            const result = await collection.deleteMany({});
            console.log(`✅ Deleted ${result.deletedCount} records`);
            totalDeleted += result.deletedCount;
          }
        } catch (error) {
          console.log(`⚠️  Error with ${collectionName}: ${error.message}`);
        }
      }
    }

    // Final stats
    const finalStats = await db.stats();
    const finalSizeMB = finalStats.dataSize / (1024 * 1024);
    const freedMB = (stats.dataSize - finalStats.dataSize) / (1024 * 1024);

    console.log('\n📊 Cleanup Results:');
    console.log('==================');
    console.log(`Total records deleted: ${totalDeleted}`);
    console.log(`Space freed: ${freedMB.toFixed(2)} MB`);
    console.log(`Final database size: ${finalSizeMB.toFixed(2)} MB`);

    if (finalSizeMB < 500) {
      console.log('✅ Database is now under 500MB limit!');
    } else {
      console.log('⚠️  Database is still over 500MB. Consider upgrading your MongoDB plan.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Cleanup completed');
  }
}

// Run the emergency cleanup
emergencyCleanup().catch(console.error);
