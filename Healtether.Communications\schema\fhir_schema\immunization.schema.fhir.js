import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});
const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
   // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});

const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});
const immunizationSchema = new Schema({
    status: String,
    type: String,
    occurrenceDateTime: String,
    primarySource: Boolean,
    lotNumber: String,
    encounter: String
});

const attachmentSchema = new Schema({
    contentType: String,
    language: String,
    data: String,
    title: String,
    creation: String
});

const documentReferenceSchema = new Schema({
    status: String,
    docStatus: String,
    type: String,
    content: [{ attachment: attachmentSchema }]
});

const ImmunizationRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    encounter: encounterSchema,
    organization: organizationSchema,
    immunizations:[immunizationSchema],
     documentReferences: [documentReferenceSchema],
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});



export { ImmunizationRecordSchema };
