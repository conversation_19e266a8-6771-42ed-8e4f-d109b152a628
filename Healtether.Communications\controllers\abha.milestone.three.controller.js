import portalDb from "../config/clinics.collections.config.js";
import {
  checkConsentStatus<PERSON>elper,
  fetchConsent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  initiateConsentRequestHelper,
  notifyConsentRequestHelper,
  sendHealthInformationRequest,
  sendNotificationRequest,
} from "../helper/abha/abha.milestone.three.helper.js";
import { onNotifyConsentM2 } from "../helper/abha/milestone.two.helper.js";
const AbhaConsentsModel = portalDb.model("abhaConsents");
export const initiateConsentRequestHIP = async (req, res) => {
  const responseBody = await initiateConsentRequestHelper(req.body.data);

  res.status(responseBody.isSuccess ? 202 : 500).json({
    message: "Consent Request Initiated",
    data: responseBody || {},
  });
};

export const checkConsentStatusHIP = async (req, res) => {
  const consentRequestId = req.body.consentRequest.id;
  const HUIId = req.headers["x-hiu-id"];
  const consentRequestData = {
    consentRequestId: consentRequestId,
  };
  await AbhaConsentsModel.updateOne(
    { requestId: req.body.response.requestId },
    {
      $set: {
        consentRequestId: consentRequestId,
      },
    }
  );

  const response = await checkConsentStatusHelper(
    consentRequestData,
    HUIId,
    req
  );
  res.status(response.isSuccess ? 202 : 500).json({
    message: response.isSuccess ? "Request Success" : "Request Failed",
    data: response,
  });
};

export const updateConsentStatus = async (data) => {
  await AbhaConsentsModel.findOneAndUpdate(
    { consentRequestId: data.consentRequest.id },
    { consentStatus: data.consentRequest.status },
    { new: true }
  );
  return {
    success: true,
    message: "Updated Consent Status",
  };
};

export const consentResponses = {}; // Temporary in-memory storage

export const storeWebhookResponseForHIUAndHIP = async (
  consentId,
  type,
  data,
  headers,
  consentReqId
) => {
  if (!consentResponses[consentId]) {
    consentResponses[consentId] = {};
  }
  consentResponses[consentId][type] = { data, headers };
  if (consentResponses[consentId].hiu && consentResponses[consentId].hip) {
    if (consentResponses[consentId].hiu.data.notification.status == "GRANTED") {
      await AbhaConsentsModel.updateOne(
        { consentRequestId: consentReqId },
        {
          $set: {
            consentArtefacts:
              consentResponses[consentId].hiu.data.notification
                .consentArtefacts,
            consentStatus:
              consentResponses[consentId].hiu.data.notification.status,
            consentStatusUpdatedOn: new Date().toISOString(),
            careContexts:
              consentResponses[consentId].hip.data.notification.consentDetail
                .careContexts,
            grantedHiTypes:
              consentResponses[consentId].hip.data.notification.consentDetail
                .hiTypes,
            consentGrantedOn:new Date(),    
            permission:
              consentResponses[consentId].hip.data.notification.consentDetail
                .permission,
          },
        }
      );
    }
    processConsentNotification(consentId);
  } else if (consentResponses[consentId].hip) {
    onNotifyConsentM2(consentId, consentResponses[consentId].hip.headers);
  }
};

const processConsentNotification = async (consentId) => {
  const { hiu, hip } = consentResponses[consentId];
  const acknowledgement = hiu.data.notification.consentArtefacts.map(
    (artefact) => ({
      status: "ok",
      consentId: artefact.id,
    })
  );
  let requestId = hip.headers["request-id"];
  const notifyData = {
    acknowledgement,
    response: {
      requestId,
    },
  };

  await notifyConsentRequestHelper(notifyData,hiu.headers["x-hiu-id"]);

  

  const fetchPromises = hiu.data.notification.consentArtefacts.map((artefact) => {
    return fetchConsentDetailsHIP(artefact.id, hiu.headers["x-hiu-id"]);
  });
  
  // Wait for all fetch operations to complete
  await Promise.all(fetchPromises);
  // delete consentResponses[consentId];
};

export const fetchConsentDetailsHIP = async (consentArtefactId, hiuId) => {
  let response = await fetchConsentDetailsHelper(consentArtefactId, hiuId);
  return { response };
};
// export const fetchConsentDetailsHIP = async (req, res) => {
//   let consentArtefactId = req.body.consentId;
//   const hiuId = req?.headers["x-hiu-id"];
//   let response = await fetchConsentDetailsHelper(consentArtefactId, hiuId);
//   return { response };
// };

export const requestHealthInformationHIP = async (req, res) => {
  const hiuId = req?.headers["x-hiu-id"];
  const response = await sendHealthInformationRequest(req.body, hiuId);

  res.status(response.isSuccess ? 202 : 500).json({
    message: response.isSuccess
      ? "Health Information Requested"
      : "Request Failed",
    data: response,
  });
};

export const notifyHealthInformationHIP = async (req, res) => {
  const result = await sendNotificationRequest(req.body.transactionId);
  const response = await result.json();
  res.status(response.isSuccess ? 202 : 500).json({
    message: response.isSuccess ? "success" : "Request Failed",
    data: response,
  });
};
