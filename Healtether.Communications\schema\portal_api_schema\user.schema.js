import mongoose from "mongoose";

const UserSchema =  new mongoose.Schema({
  firstName:{
    type: String,
    index:true,
    required: true, 
    maxLength: 255
  },
  lastName:{
    type: String,
    index:true,
    required: true, 
    maxLength: 255
  },
  email: {
    type: String,
    index:true,
  },
  mobile: {
    type: String,
    required:true,
    unique: true, 
    index:true,
    maxLength: 12
  },
  countryCode:{
    type: String,
    default:'+91',
    maxLength: 5
  },
  password: {
    type: String,
    maxLength: 1000
  },
  active:{
    type:Boolean,
    default:true
  },
  isdeleted:{
    type:Boolean,
    default:false
  },
  isSuperAdmin:{
    type:Boolean,
    default:false
  },
  lastLoginAttempt:{
    type:Date,
  }
},
{versionKey:'1.2'});

UserSchema.virtual('staffDetail', {
  ref: 'Staff',  
  localField: '_id', //userId
  foreignField: 'userId',
  justOne:true
});
UserSchema.virtual('adminUserClinic', {
  ref: 'Client',
  localField: '_id',  //userId
  foreignField: 'adminUserId',
  justOne:true
});
UserSchema.virtual('linkedClinics', {
  ref: "LinkClientUser",
  localField: '_id',  // userId
  foreignField: 'userId', // field in clientUser

});

// Create the user model
//const User = new mongoose.model("User", userSchema);   
export { UserSchema };

const ClientUserSchema=new mongoose.Schema({
 
  isAdmin:{
    type:Boolean,
    default:false
  },
  userId: { type: mongoose.Schema.Types.ObjectId,
         ref: 'User' },   
  clinic: {
    type: mongoose.Schema.Types.ObjectId,
     ref: 'Client'
  },
  isdeleted:{
    type:Boolean,
    default:false
  }
},{versionKey:'1.1'});
ClientUserSchema.index({ userId:1, clinic: 1}, { unique: true });
ClientUserSchema.virtual('linkedStaff', {
  ref: "Staff",
  localField: 'userId',  // userId
  foreignField: 'userId', // field in clientUser

});

//const ClientUser = new mongoose.model("LinkClientUser", clientUserSchema);   
export { ClientUserSchema };

