import { v4 as uuidv4 } from "uuid";

import { encounterDiv, encounterIdentifier, encounterMetadata, encounterClass, getSnomedCtCode } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");

export const generateEncounterResource = async (currentTime, patientResource, conditionResources, encounter, conditions, general, patientId) => {
    const id = uuidv4();
    const prescription = await Prescription.findOne({ patient: patientId }).sort({
    "created.on": -1,
    });

    const diagnosis = await Promise.all(conditions.map(async (cond) => {
        const matchedCondition = conditionResources.find(resource =>{
          return  resource.resource.code.coding.some(coding => {
               return coding.display.toLowerCase() === cond.type.toLowerCase()})}
        );

        if (!matchedCondition){
            console.log("no match found for condition");
            return null
        } ;

        const snomedData = await generateSnomedCtCode(cond.type);
        const notes = `(Notes: ${prescription.diagnosis.filter(diag=>diag.name===snomedData.term).map(d => d.notes).join(" ")})`;

        return {
            condition: {
                reference: `urn:uuid:${matchedCondition.resource.id}`,
                display: matchedCondition.resource.resourceType
            },
            use: getSnomedCtCode(snomedData.conceptId, snomedData.term, notes)
        };
    }));
    const filteredDiagnosis = diagnosis.filter(d => d !== null);
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Encounter',
            id,
            meta: encounterMetadata(currentTime),
            identifier: general.hipIds.map(encounterIdentifier),
            status: encounter?.status,
            class: encounterClass(),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            period: {
                start: encounter.startTime,
                end:encounter.endTime
            },
            ...(filteredDiagnosis.length > 0 && { diagnosis: filteredDiagnosis })
            // text: encounterDiv()
        }
    }
}