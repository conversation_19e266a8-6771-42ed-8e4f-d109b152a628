import mongoose from "mongoose";

export const ClinicGroupSchema = new mongoose.Schema({
  groupName: {
    type: String,
    required: true,
  },
  created: {
    on: {
      type: Date,
      default: Date.Now,
    },
    by: {
      id: String,
      name: {
        type: String,
        maxLength: 255,
      },
    },
  },
  deleted: {
    type: Boolean,
    default: false,
  },
});

// const ClinicGroup = new mongoose.model("ClinicGroup",clinicGroupSchema);
// export { ClinicGroup };
