import moment from "moment";
import { convertData } from "../controllers/appointments/write-prescription.controller.js";
import { getAppointment } from "../helpers/appointment/appointment.helper.js";
import { getMedicalHistoryForPatient } from "../helpers/appointment/write-prescription/medical-histories.helper.js";
import { getPrescriptionAndVitalsForAppointment } from "../helpers/appointment/write-prescription/prescription.helper.js";
import { getClientById } from "../helpers/clinic/client.helper.js";
import { getPatientDetail } from "../helpers/patient/patient.helper.js";
import { staffById } from "../helpers/staff/staff.helper.js";
import { Invoice } from "../model/clinics.model.js";
import { fetchImageAsBase64 } from "./common.utils.js";
import fs from "fs";

import { v4 as uuidv4 } from "uuid";
import path from "path";
import { log } from "console";
// Function to create general details
const createGeneralDetails = (artifact, hipUrl, hipIds, status, clientId) => ({
  artifact: artifact || "OPConsultRecord",
  hipUrl: hipUrl || "https://www.healtether.com",
  hipIds: hipIds || ["hpi1", "hip2"],
  status: status || "final",
  clientId: clientId || "SBX_003515",
});

// Function to create patient details
const createPatientDetails = (
  id,
  abhaNumber,
  abhaAddress,
  name,
  gender,
  dob,
  address,
  doctors,
  allergyIntolerances,
  telecom
) => {
  // console.log("address", address.landmarks);
  return {
    id: id,
    abhaNumber: abhaNumber,
    abhaAddress: abhaAddress,
    name: name,
    gender: gender?.toLowerCase(),
    dob: moment(dob).format("YYYY-MM-DD"),
    address: [
      {
        use: "home",
        type: "physical",
        postalCode: address.pincode,
        country: "india",
        district: address.landmarks,
        city: address.city,
        state: address.landmarks,
        text: address.house,
      },
    ],
    doctors: doctors,
    allergyIntolerances: Array.isArray(allergyIntolerances)
      ? allergyIntolerances?.map((item) => ({
          type: item.name,
          clinicalStatus: "active",
          verificationStatus: "confirmed",
          notes: [item.notes], // Wrapping notes in an array
          doctor: doctors[0], // Assuming doctor is constant, update dynamically if needed
        }))
      : [],
    telecom: [
      {
        system: "phone",
        value: telecom,
        use: "mobile",
      },
    ],
  };
};

const createPractitionerDetails = (practitionerData, patientId) => {
  // console.log("address", practitionerData.address);
  return {
    names: [`${practitionerData.firstName + practitionerData.lastName}`] || [
      "Dr. Smith",
    ],
    licenses: [
      {
        code: "MD",
        display: "Medical License number",
        licNo: practitionerData.hprId || "123",
      },
    ],
    telecom: [
      {
        system: "phone",
        value: practitionerData.mobile || "+91-**********",
        use: "mobile",
      },
    ],
    gender: practitionerData.gender?.toLowerCase(),
    birthDate:
      moment(practitionerData.birthDate).format("YYYY-MM-DD") || "1993-10-03",
    patient: patientId || "patient123",
    address: [
      {
        use: "home",
        type: "physical",
        postalCode: practitionerData.address.pincode,
        country: "india",
        district: practitionerData.address.landmarks,
        city: practitionerData.address.city,
        state: practitionerData.address.landmarks,
        text: practitionerData.address.house,
      },
    ] || [
      {
        use: "home",
        type: "physical",
        text: "dummy address line, dummy city, dummy district, dummy state",
        line: ["dummy address line", "1", "2", "3"],
        city: "dummy city",
        district: "dummy district",
        state: "dummy state",
        postalCode: "625513",
        country: "india",
        // period: {
        //   start: new Date().toISOString(),
        //   end: new Date().toISOString(),
        // },
      },
    ],
  };
};
const createChargeItems = (type, quantity, id) => ({
  id: id,
  type: type || "consultation",
  status: "billed",
  quantity: quantity || 1,
});

const createInvoiceDetails = (
  id,
  status,
  date,
  totalNet,
  totalGross,
  invoice
) => ({
  id: id || "3434545",
  status: status || "issued",
  date: date || new Date().toISOString().split("T")[0],
  totalNet: totalNet || { value: 2752, currency: "INR" },
  totalGross: totalGross || { value: 2575, currency: "INR" },
  lineItem:
    invoice.treatments.length > 0 &&
    invoice.treatments.map((treatment) => {
      return {
        type: treatment.treatment,
        priceComponent: [
          {
            type: "base",
            amount: {
              value: parseFloat(invoice.totalTax.toString()),
              currency: "INR",
            },
          },
          {
            type: "discount",
            amount: {
              value: parseFloat(invoice.discount.toString()),
              currency: "INR",
            },
          },
          {
            type: "tax",
            display: "CGST",
            amount: {
              value: parseFloat(treatment.cgstAmount.toString()),
              currency: "INR",
            },
          },
          {
            type: "tax",
            display: "SGST",
            amount: {
              value: parseFloat(treatment.sgstAmount.toString()),
              currency: "INR",
            },
          },
        ],
      };
    }),
});

const serviceRequests = (status, intent, category, type) => ({
  status: status || "completed",
  intent: intent || "order",
  categories: category || ["Blood Test"],
  type: type || "laboratory Test",
});
const createCondition = (type) => ({
  type: type || "Hypertension",
  status: "active",
  recordedDate: new Date().toISOString(),
  startDate: new Date().toISOString(),
  endDate: new Date().toISOString(),
});

const createMedicationStatement = (status, type) => ({
  status: status || "completed",
  type: type || "Telmisartan 20 mg oral tablet",
});

const createMedicationRequest = (drugs, diagnosis, symptoms) => ({
  status: "active",
  intent: "order",
  authoredOn: new Date().toISOString(),
  medication: drugs.drugName || "",
  forCondition: diagnosis.map((diagnosis) => diagnosis.name) || [],
  reason: symptoms.map((symptom) => symptom.name) || [],
  dosageInstruction: [
    {
      text: drugs.notes || "",
      // repeat: {
      //   frequency: drugs.frequency,
      //   period: drugs.duration.value||0,
      //   period: drugs.duration.value||0,
      //   periodUnit: drugs.duration.unit,
      // },
      // route: "Oral",
      // doseQuantity: {
      //   value: drugs.dosage,
      //   unit: "tablet",
      // },
      // site: "Mouth",
      additionalInstruction: drugs.content || "",
    },
  ],
});
// Function to create encounter details
const createEncounterDetails = (appointmentData) => {
  return {
    status: "finished",
    startTime: appointmentData?.created?.on || "1999-12-01T10:00:00+05:30",
    endTime: appointmentData?.ended?.on || "2023-11-01T10:00:00+05:30",
  };
};

// Function to create organization details
const createOrganizationDetails = (clinicData) => {
  console.log("clinicData createOrganizationDetails", clinicData);
  return {

  name:clinicData?.clinicName|| "Health Organization",
  telecom: [
    {
      system: "phone",
      value: clinicData?.adminUserId?.mobile || "+91-**********",
      use: "work",
    },
  ],
  licenses: [
    {
      code: "PRN",
      display: "Provider number",
      licNo: clinicData?.hfrId || "SBX_003515",
    },
  ],
}};

// Function to create procedure details
const createProcedure = (status, type, performedDateTime, followUp) => ({
  status: status || "completed",
  type: type || "Electrocardiogram",
  performedDateTime: performedDateTime || new Date().toISOString(),
  followUp: ["Follow-up consultation"],
});
const createAppointmentDetails = (appointmentData, prescription) => ({
  status: "booked",
  serviceCategories: ["Consultation"],
  serviceTypes: ["General"],
  specialty: [appointmentData.speciality] || ["Cardiology", "Anesthetics"],
  appointmentType: "consultation",
  description: appointmentData.reason || "Follow-up consultation",
  start: appointmentData.started.on || "2023-10-01T10:00:00+05:30",
  end: appointmentData.started.ended || "2023-10-01T11:00:00+05:30",
  created: appointmentData.created.on || "2023-09-30T10:00:00+05:30",
  reasonReference: prescription.prescriptions.diagnosis.map((symptom) => {
    return symptom.name;
  }) || ["Hypertension"],
  basedOnServices: prescription.prescriptions.labTests.map((labTest) => {
    return labTest.name;
  }) || ["Blood Test"],
});

const createDocumentReference = async (content,clinicId,type,title) => {
  const base64Data = await fetchImageAsBase64(content,clinicId);
  // console.log("base64Data", base64Data);
  return {
    status: "current",
    docStatus: "final",
    type: type||"Clinical consultation report",
    content: [
      {
        attachment: {
          contentType: "application/pdf",
          language: "en",
          data: base64Data,
          title: title||"Consultation Report",
          creation: new Date().toISOString(),
        },
      },
    ],
  };
};
// Function to create signature details
const createSignatureDetails = (data, doctor) => ({
  who: { type: "Practitioner", name: doctor[0] || "Dr. Smith" },
  sigFormat: "image/jpeg",
  data: data || "c2lnbmF0dXJlIGRhdGEgaGVyZQ==",
});

const mapVitalsToObservations = (vitals) => {
  const vitalMappings = {
    bloodPressure: {
      systolic: {
        code: "8480-6",
        display: "Systolic Blood Pressure",
        unit: "mmHg",
        unitCode: "mm[Hg]",
      },
      diastolic: {
        code: "8462-4",
        display: "Diastolic Blood Pressure",
        unit: "mmHg",
        unitCode: "mm[Hg]",
      },
    },
    height: {
      code: "8302-2",
      display: "Body Height",
      unit: "in",
      unitCode: "[in_i]",
    },
    pulseRate: {
      code: "8867-4",
      display: "Heart Rate",
      unit: "beats/minute",
      unitCode: "/min",
    },
    rbs: {
      code: "2339-0",
      display: "Glucose [Mass/volume] in Blood",
      unit: "mg/dL",
      unitCode: "mg/dL",
    },
    respiratoryRate: {
      code: "9279-1",
      display: "Respiratory Rate",
      unit: "breaths/min",
      unitCode: "/min",
    },
    spo2: {
      code: "2708-6",
      display: "Oxygen saturation in Arterial blood",
      unit: "%",
      unitCode: "%",
    },
    temperature: {
      code: "8310-5",
      display: "Body Temperature",
      unit: "Cel",
      unitCode: "Cel",
    },
  };

  const effectiveDateTime = new Date().toISOString(); // Current timestamp

  let observations = [];

  // Map blood pressure separately
  if (vitals.bloodPressure) {
    observations.push({
      bloodPressure: [
        {
          status: "final",
          code: {
            code: vitalMappings.bloodPressure.systolic.code,
            display: vitalMappings.bloodPressure.systolic.display,
          },
          valueQuantity: {
            value: vitals.bloodPressure.systolic,
            unit: vitalMappings.bloodPressure.systolic.unit,
            code: "mm[Hg]",
          },
          effectiveDateTime,
          interpretation:vitals.interpretation,
        },
        {
          status: "final",
          code: {
            code: vitalMappings.bloodPressure.diastolic.code,
            display: vitalMappings.bloodPressure.diastolic.display,
          },
          valueQuantity: {
            value: vitals.bloodPressure.diastolic,
            unit: vitalMappings.bloodPressure.diastolic.unit,
            code: "mm[Hg]",
          },
          effectiveDateTime,
          interpretation:vitals.interpretation,
        },
      ],
    });
  }

  // Map other vitals
  Object.keys(vitalMappings).forEach((key) => {
    // Skip bloodPressure since it's already handled
    if (key !== "bloodPressure" && vitals[key] !== undefined) {
      observations.push({
        status: "final",
        code: {
          code: vitalMappings[key].code,
          display: vitalMappings[key].display,
        },
        valueQuantity: {
          value: vitals[key],
          unit: vitalMappings[key].unit,
          code: vitalMappings[key].unitCode, // Using unit as code
        },
        effectiveDateTime,
      });
    }
  });

  return observations;
};
const diagnosticReport = () => ({
  status: "final",
  issued: "2023-10-01T10:30:00+05:30",
  encounter: "encounter id",
  result: [
    {
      id: "observation-id-1",
      display: "Observation/Cholesterol",
    },
  ],
  conclusion: "All tests are normal.",
  categories: ["Blood Test"],
  type: "laboratory Test",
  identifier: {
    system: "https://www.healtether.com/lab",
    value: "32432432",
  },
  presentedForm: [
    {
      attachment: {
        contentType: "application/pdf",
        language: "en",
        data: "VGhpcyBpcyBhIGR1bW15IEJhc2U2NCBlbmNvZGVkIGRvY3VtZW50Lg==",
        title: "Consultation Report - John Doe",
        creation: "2023-10-01T10:30:00+05:30",
      },
    },
  ],
});

// Function to assemble the complete structure
export const createStructuredData = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  const allDocuments = [
    ...appointmentData.medicalRecords,
    ...appointmentData.procedureRecords,
    ...appointmentData.prescriptionRecords,
  ];


  // fetchImageAsBase64(appointmentData.prescriptionReport[0].blobName, appointmentData?.clinic);
  const documentReferencePromises =
    allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName)
          .map((item) => {
            return createDocumentReference(item.blobName,clinicData._id );
          })
      : [];

  // Wait for all document reference promises to resolve
  const documentReferences = await Promise.all(documentReferencePromises);

  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(appointmentData),
    organization: createOrganizationDetails(clinicData),
    appointment: createAppointmentDetails(appointmentData, prescription),
    practitioners: [createPractitionerDetails(practitionerData, patient._id)],
    serviceRequests: prescription.prescriptions.labTests.map((labtest) => {
      return serviceRequests("completed", "order", [`${labtest.name}`]);
    }),
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationStatements: [createMedicationStatement()],
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure(
        "completed",
        procedure.name,
        "2023-10-01T10:30:00+05:30"
      );
    }),
    documentReferences: documentReferences,
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createDischargeSummaryStructuredData = (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(),
    organization: createOrganizationDetails(clinicData),
    appointment: createAppointmentDetails(appointmentData, prescription),
    practitioners: [createPractitionerDetails(practitionerData)],
    serviceRequests: prescription.prescriptions.labTests.map((labtest) => {
      return serviceRequests("completed", "order", [`${labtest.name}`]);
    }),
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationStatements: [createMedicationStatement()],
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure("completed", "procedure", procedure.name);
    }),
    dischargeSummary: [],
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

const createBinaryDetails = (type, data) => {
  return {
    contentType: type,
    data: data,
  };
};

export const createPrescriptionStructuredData = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {

  const base64Data = await fetchImageAsBase64(appointmentData.prescriptionReport[0].blobName, appointmentData?.clinic);
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(appointmentData),
    organization: createOrganizationDetails(clinicData),
    practitioners: [createPractitionerDetails(practitionerData)],
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure("completed", "procedure", procedure.name);
    }),
    binary: createBinaryDetails("application/pdf", base64Data),
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createHealthRecordStructuredData = async (
  artifact,
  clinicData,
  appointmentData,
  patient,
  practitionerData
) => {
  const allDocuments = [
    ...(appointmentData.medicalRecords || []),
    ...(appointmentData.procedureRecords || []),
    ...(appointmentData.prescriptionRecords || []),
  ];
  const documentReferencePromises =
    allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName) // Ensure blobName exists
          .map((item) => {
            return createDocumentReference(item.blobName,clinicData._id,"Wellness Record","Laboratory report" );
          })
      : [];

  const documentReferences = await Promise.all(documentReferencePromises);

  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      [],
      patient.mobile
    ),
    organization: createOrganizationDetails(clinicData),
    practitioners: [createPractitionerDetails(practitionerData)],
    encounter: createEncounterDetails(appointmentData),
    documentReferences: documentReferences,
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createImmunizationRecordStructuredData = async (
  artifact,
  clinicData,
  appointmentData,
  patient,
  practitionerData,
  immunizationPrescriptions
) => {
  const allDocuments = [
    ...(appointmentData.vaccineCertificate || []),
  ];
    const documentReferencePromises =
    allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName)
          .map((item) => {
            return createDocumentReference(item.blobName,clinicData._id );
          })
      : [];

  // Wait for all document reference promises to resolve
  const documentReferences = await Promise.all(documentReferencePromises);

  const immunizations =
    immunizationPrescriptions.length > 0 &&
    immunizationPrescriptions.map((vaccine) => ({
      status: "completed",
      type: vaccine.drugName || "Unknown Vaccine",
      occurrenceDateTime: vaccine.occurrenceDateTime
        ? new Date(vaccine.occurrenceDateTime).toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0],
      primarySource: true,
      lotNumber: vaccine.lotNumber?.toString() || "N/A",
    }));
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      [],
      patient.mobile
    ),
    organization: createOrganizationDetails(clinicData),
    practitioners: [createPractitionerDetails(practitionerData)],
    encounter: createEncounterDetails(appointmentData),
    documentReferences:documentReferences,
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
    ...(immunizations.length > 0 && { immunizations }),
  };
};

export const createWellnessStructuredData = (
  artifact,
  patient,
  practitionerData,
  prescription,
  medicalHistory,
  clinicData,
  appointmentData,
) => {
  return ({
  general: createGeneralDetails(
    artifact,
    "https://www.healtether.com",
    ["hip1", "hip2"],
    "final",
    "SBX_003515"
  ),
  patient: createPatientDetails(
    patient._id,
    patient.abhaNumber,
    patient.abhaAddress,
    {
      text: `${patient.firstName + patient.lastName}`,
      prefix: [`${patient.prefix}`],
    },
    patient.gender,
    patient.birthday,
    patient?.address,
    [`${practitionerData.firstName + practitionerData.lastName}`],
    medicalHistory?.allergies,
    patient.mobile
  ),
  practitioners: [createPractitionerDetails(practitionerData)],
  organization: createOrganizationDetails(clinicData),
  observations: mapVitalsToObservations(prescription.vitals),
  encounter: createEncounterDetails(appointmentData),
  signature: createSignatureDetails("", [
    `${practitionerData.firstName + practitionerData.lastName}`,
  ]),
})
};
export const createStructureForInvoice = async (
  artifact,
  clinicData,
  patient,
  practitionerData,
  invoiceData,
  appointmentData
) => {
  const base64Data = await fetchImageAsBase64(appointmentData.invoiceReport[0].blobName, appointmentData?.clinic);



  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      [],
      patient.mobile
    ),
    practitioners: [createPractitionerDetails(practitionerData)],
    chargeItems: invoiceData.treatments.map((invoice) => {
      return createChargeItems(
        invoice.treatment,
        invoice.quantity,
        invoice._id
      );
    }),
    invoice: createInvoiceDetails(
      invoiceData._id,
      "issued",
      new Date().toISOString().split("T")[0],
      { value: parseFloat(invoiceData.totalCost.toString()) + parseFloat(invoiceData.discount.toString()), currency: "INR" },
      { value: parseFloat(invoiceData.totalCost.toString()),  currency: "INR" },
      invoiceData
    ),
    encounter: createEncounterDetails(),
    organization: createOrganizationDetails(clinicData),
   binary: createBinaryDetails("application/pdf", base64Data),
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};


export const bundleFhir = async (req, res) => {
  try {
    const { clinicId, appointmentId, patientId, type } = req.query;
    const apiUrl = process.env.WHATSAPP_API_URL;

    console.log("Processing FHIR bundle request:", {
      clinicId,
      appointmentId,
      patientId,
      type,
      apiUrl,
    });

    const [
      prescription,
      medicalHistory,
      clinicData,
      patientData,
      appointmentData,
      invoiceData,
    ] = await Promise.all([
      getPrescriptionAndVitalsForAppointment(clinicId, appointmentId),
      getMedicalHistoryForPatient(patientId),
      getClientById(clinicId),
      getPatientDetail(patientId),
      getAppointment(appointmentId),
      Invoice.findOne({ appointmentId }),
    ]);

    console.log("FHIR Debug: Retrieved data for bundle", {
      hasPatientData: !!patientData,
      patientId: patientData?._id,
      patientAbhaNumber: patientData?.abhaNumber,
      patientAbhaAddress: patientData?.abhaAddress,
      patientName: `${patientData?.firstName} ${patientData?.lastName}`,
      timestamp: new Date().toISOString()
    });

    const actualPractitionerData = await staffById(appointmentData.doctorId);

    const immunizationPrescriptions =
      prescription?.prescriptions?.drugPrescriptions?.filter((drug) =>
        drug.drugName?.toLowerCase().includes("vaccine")
      );
    console.log("immunizationPrescriptions123", immunizationPrescriptions);
    // Determine which structured data to create based on type parameter
    const structuredDataPromises = [];

    if (!type || type === "all") {
      // Create all records if no specific type is provided or type is 'all'
      structuredDataPromises.push(
        createStructuredData(
          "OPConsultRecord",
          clinicData,
          patientData,
          appointmentData,
          actualPractitionerData,
          prescription,
          medicalHistory
        ),
        createStructureForInvoice(
          "InvoiceRecord",
          clinicData,
          patientData,
          actualPractitionerData,
          invoiceData,
          appointmentData
        ),
        createPrescriptionStructuredData(
          "PrescriptionRecord",
          clinicData,
          patientData,
          appointmentData,
          actualPractitionerData,
          prescription,
          medicalHistory
        ),
        createHealthRecordStructuredData(
          "HealthDocumentRecord",
          clinicData,
          appointmentData,
          patientData,
          actualPractitionerData
        ),
        createImmunizationRecordStructuredData(
          "ImmunizationRecord",
          clinicData,
          appointmentData,
          patientData,
          actualPractitionerData,
          immunizationPrescriptions
        ),
        Promise.resolve(
          createWellnessStructuredData(
            "WellnessRecord",
            patientData,
            actualPractitionerData,
            prescription,
            appointmentData,
            medicalHistory,
            clinicData,
          )
        )
      );
    } else if (type === "op") {
      // Only create OPConsultation and Immunization records if type is 'op'
      structuredDataPromises.push(
        createStructuredData(
          "OPConsultRecord",
          clinicData,
          patientData,
          appointmentData,
          actualPractitionerData,
          prescription,
          medicalHistory
        ),
        createImmunizationRecordStructuredData(
          "ImmunizationRecord",
          clinicData,
          appointmentData,
          patientData,
          actualPractitionerData,
          immunizationPrescriptions
        ),
        createPrescriptionStructuredData(
          "PrescriptionRecord",
          clinicData,
          patientData,
          appointmentData,
          actualPractitionerData,
          prescription,
          medicalHistory
        ),
        Promise.resolve(
          createWellnessStructuredData(
            "WellnessRecord",
            patientData,
            actualPractitionerData,
            prescription,
            appointmentData,
            medicalHistory,
            clinicData
          )
        ),
        createHealthRecordStructuredData(
          "HealthDocumentRecord",
          clinicData,
          appointmentData,
          patientData,
          actualPractitionerData

        )
      );
    } else if (type === "prescription") {
      // Only create Prescription record if type is 'prescription'
      structuredDataPromises.push(
        createPrescriptionStructuredData(
          "PrescriptionRecord",
          clinicData,
          patientData,
          appointmentData,
          actualPractitionerData,
          prescription,
          medicalHistory
        )
      );
    } else if (type === "invoice") {
      // Only create Invoice record if type is 'invoice'
      structuredDataPromises.push(
        createStructureForInvoice(
          "InvoiceRecord",
          clinicData,
          patientData,
          actualPractitionerData,
          invoiceData,
          appointmentData
        )
      );
    } else if (type === "wellness") {
      // Only create Wellness record if type is 'wellness'
      structuredDataPromises.push(
        Promise.resolve(
          createWellnessStructuredData(
            "WellnessRecord",
            patientData,
            actualPractitionerData,
            prescription,
            appointmentData,
            medicalHistory,
            clinicData
          )
        )
      );
    } else if (type === "health") {
      // Only create Health Document record if type is 'health'
      structuredDataPromises.push(
        createHealthRecordStructuredData(
          "HealthDocumentRecord",
          clinicData,
          appointmentData,
          patientData,
          actualPractitionerData
        )
      );
    }

    // Execute all the selected promises
    const structuredDataResults = await Promise.all(structuredDataPromises);

    console.log("FHIR Debug: Structured data results", {
      resultsCount: structuredDataResults.length,
      type,
      timestamp: new Date().toISOString()
    });

    // Log each structured data result
    structuredDataResults.forEach((result, index) => {
      console.log(`FHIR Debug: Structured data result ${index}`, {
        hasPatient: !!result?.patient,
        patientType: typeof result?.patient,
        patientAbhaNumber: result?.patient?.abhaNumber,
        patientAbhaAddress: result?.patient?.abhaAddress,
        timestamp: new Date().toISOString()
      });
    });

    // Create an object to map each result to its type
    const allStructuredData = {};

    // Map the results based on type
    if (!type || type === "all") {
      allStructuredData.opConsult = structuredDataResults[0];
      allStructuredData.invoice = structuredDataResults[1];
      allStructuredData.prescription = structuredDataResults[2];
      allStructuredData.healthDocument = structuredDataResults[3];
      allStructuredData.immunizations = structuredDataResults[4];
      allStructuredData.wellness = structuredDataResults[5];
    } else if (type === "op") {
      allStructuredData.opConsult = structuredDataResults[0];
      if (immunizationPrescriptions && immunizationPrescriptions.length > 0) {
        allStructuredData.immunizations = structuredDataResults[1];
      }
      allStructuredData.prescription = structuredDataResults[2];
      allStructuredData.wellness = structuredDataResults[3];
      allStructuredData.healthDocument = structuredDataResults[4];
    } else if (type === "invoice") {
      allStructuredData.invoice = structuredDataResults[0];
    } else if (type === "health") {
      allStructuredData.healthDocument = structuredDataResults[0];
    }

    // console.log(
    //   "Structured data created successfully:",
    //   Object.keys(allStructuredData)
    // );



    console.log(
      `Starting ${Object.keys(allStructuredData).length} FHIR API requests...`
    );

    // Execute API calls with overall timeout monitoring
    let timeoutId;
    const timeoutPromise = new Promise((resolve) => {
      timeoutId = setTimeout(() => {
        resolve("API_TIMEOUT");
      }, 120000); // 120 second overall timeout (increased)
    });
    const filePath = path.join("./", "fhirDischargeBundle.json");
    fs.writeFileSync(
      filePath,
      JSON.stringify(allStructuredData, null, 2),
      "utf-8"
    );
    const apiCallPromises = Object.entries(allStructuredData).map(
      ([type, data]) => callApi(type, data, apiUrl)
    );

    const resultsOrTimeout = await Promise.race([
      Promise.all(apiCallPromises),
      timeoutPromise,
    ]);

    // Clear the timeout to prevent memory leaks
    clearTimeout(timeoutId);

    if (resultsOrTimeout === "API_TIMEOUT") {
      console.error("API requests timed out after 120 seconds");
      return res.status(504).json({
        message: "Bundle FHIR operations timed out",
        error: "Request timed out after 120 seconds",
      });
    }

    const results = resultsOrTimeout;

    // Count successes and failures
    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    console.log(
      `FHIR API requests completed: ${successCount} successful, ${failureCount} failed`
    );

    // Process results
    results.forEach((result) => {
      if (result.success) {
        console.log(`Success for ${result.type}:`, result.data);
      } else {
        console.error(`Failed for ${result.type}:`, result.error);
      }
    });

    // Only link care contexts if patient has ABHA information
    if (patientData.abhaNumber && patientData.abhaAddress) {
      if (results && !patientData.linkingToken) {
        await generateAbhaLinkToken(patientData, apiUrl);
      }

      const careContextData = results
        .filter((data) => data.success)
        .map((data) => {
          return {
            careContexts: [
              {
                display: `${data.type}-${moment(new Date()).format(
                  "YYYY-MM-DD:HH:mm:ss"
                )}`,
                referenceNumber: data.data.response.fhirId,
              },
            ],
            count: 1,
            display: `${data.type}-${moment(new Date()).format(
              "YYYY-MM-DD:HH:mm:ss"
            )}`,
            hiType:
              data.type == "wellness"
                ? "WellnessRecord"
                : data.type == "invoice"
                  ? "Invoice"
                  : data.type == "healthDocument"
                    ? "HealthDocumentRecord"
                    : data.type == "opConsult"
                      ? "OPConsultation"
                      : data.type == "dischargeSummary"
                        ? "DischargeSummary"
                        : data.type == "prescription"
                          ? "Prescription"
                          : data.type == "immunizations"
                            ? "ImmunizationRecord"
                            : "",
            referenceNumber: uuidv4(),
          };
        });


      console.log("careContextData", careContextData);

      // Only proceed with care context linking if we have successful results
      if (!careContextData || careContextData.length === 0) {
        console.log("No successful FHIR results to link as care context - skipping care context linking");
        // Don't throw error, just skip care context linking
      } else {

      try {
        const careContextResponse = await fetch(
          `${apiUrl}/m2/link-care-context`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-hip-id": process.env.HIP_ID || "IN2410000949",
              "x-cm-id": "sbx",
              "x-link-token": patientData.linkingToken,
            },
            body: JSON.stringify({
              abhaNumber: patientData.abhaNumber
                .replace(/-/g, "")
                .replace(/ /g, ""),
              abhaAddress: patientData.abhaAddress,
              patient: careContextData,
            }),
          }
        );

        console.log("Response from care context linking", careContextResponse);

        if (!careContextResponse.ok) {
          const errorText = await careContextResponse.text();
          console.error(
            `Care context linking failed: ${careContextResponse.status} ${careContextResponse.statusText}`,
            errorText
          );
        } else {
          const responseData = await careContextResponse.json();
          console.log("Care context linking successful:", responseData);
        }
      } catch (error) {
        console.error("Error during care context linking:", error);
      }
      }
    }

    // // Return results to client
    res.status(200).json({
      message: "Bundle FHIR operations completed",
      type: type || "all",
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount,
      },
      results,
    });
  } catch (error) {
    console.error("Error in bundleFhir:", error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
};


 const callApi = async (type, data, apiUrl) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // Increased to 60 seconds
      try {
        console.log(`FHIR Debug: Sending ${type} data to FHIR API`, {
          type,
          hasPatient: !!data?.patient,
          patientType: typeof data?.patient,
          patientAbhaNumber: data?.patient?.abhaNumber,
          patientAbhaAddress: data?.patient?.abhaAddress,
          timestamp: new Date().toISOString()
        });

        // Add retry logic for better reliability
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries) {
          try {
            const response = await fetch(
              `${apiUrl}/abha/fhir?skipFhirProcessing=true`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json"
                },
                body: JSON.stringify(data),
                signal: controller.signal,
              }
            );

            clearTimeout(timeoutId);

            if (!response.ok) {
              const errorText = await response.text();
              console.error(
                `API returned error for ${type}:`,
                response.status,
                errorText
              );
              return {
                type,
                success: false,
                error: `API Error: ${response.status} ${response.statusText}`,
                details: errorText,
              };
            }

            const responseData = await response.json();
            console.log(`Successfully processed ${type} data`);
            return { type, success: true, data: responseData };

          } catch (fetchError) {
            retryCount++;

            if (retryCount <= maxRetries) {
              console.log(`Retry ${retryCount}/${maxRetries} for ${type} after error:`, fetchError.message);
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
              continue;
            }

            throw fetchError;
          }
        }

      } catch (error) {
        clearTimeout(timeoutId);
        console.error(`Error calling API for ${type}:`, error);

        // Create more detailed error response
        const errorDetails = {
          message: error.message,
          code: error.code || "UNKNOWN",
          name: error.name,
          stack:
            process.env.NODE_ENV === "development" ? error.stack : undefined,
        };

        // Handle connection refused specifically
        if (
          error.code === "ECONNREFUSED" ||
          error.message.includes("refused")
        ) {
          console.error(
            `Connection refused to API server. Check if the server at ${apiUrl} is running.`
          );
          return {
            type,
            success: false,
            error: "Connection refused. API server may be down or unreachable.",
            details: errorDetails,
          };
        }

        // Handle aborted requests
        if (error.name === "AbortError") {
          return {
            type,
            success: false,
            error: "Request timed out after 60 seconds",
            details: errorDetails,
          };
        }

        // Handle timeout errors specifically
        if (error.code === "UND_ERR_HEADERS_TIMEOUT" || error.message.includes("timeout")) {
          console.error(`Timeout error for ${type}:`, error.message);
          return {
            type,
            success: false,
            error: "Request timed out - Communications service may be overloaded",
            details: errorDetails,
          };
        }

        return {
          type,
          success: false,
          error: error.message,
          details: errorDetails,
        };
      }
    };


export async function generateAbhaLinkToken(patientData, apiUrl) {
  try {
    const abhaNumber = patientData.abhaNumber?.replace(/-/g, "") || null;

    if (!abhaNumber && !patientData.abhaAddress) {
      console.log(
        "Skipping ABHA link token generation - no ABHA number or address available"
      );
      return;
    }

    const requestBody = {
      abhaNumber,
      abhaAddress: patientData.abhaAddress || null,
      name: `${patientData.firstName} ${patientData.lastName}`,
      gender: patientData.gender[0],
      yearOfBirth: new Date(patientData.birthday).getFullYear(),
    };

    console.log("Generating ABHA linking token for patient:", requestBody.name);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    const response = await fetch(`${apiUrl}/m2/generate-linking-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-hip-id": process.env.HIP_ID,
        "x-cm-id": "sbx",
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        "ABHA linking token generation failed:",
        response.status,
        errorText
      );
      return;
    }

    const responseData = await response.json();
    console.log("ABHA linking token generated successfully:", responseData);
    return responseData;
  } catch (error) {
    console.error("Error generating ABHA linking token:", error);
  }
}
