import { v4 as uuidv4 } from "uuid";
import {
  getSnomedCtCode,
  procedureMetadata,
  procedureDiv,
} from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import portalDb from "../../../config/clinics.collections.config.js";
import mongoose from "mongoose";
const Pastprocedures = portalDb.model("Pastprocedures");

export const generateProcedureResource = async (
  status,
  type,
  performedDateTime,
  followUp,
  patientResource,
  patientId
) => {
  const id = uuidv4();
  let notes;

  const getSnomedDataType = await generateSnomedCtCode(type);
  const procedure = await Pastprocedures.findOne({
    patient: new mongoose.Types.ObjectId(patientId),
    name: getSnomedDataType.term,
  });
  if(procedure && procedure.note){
    const note = procedure.notes;
    const duration = `${procedure.duration.value} ${procedure.duration.unit}`;
    notes = `(Duration:${duration}) (Notes: ${note})`;
  }

  const followUpData = await Promise.all(
    followUp.map(async (followUpItem) => {
      const snomedData = await generateSnomedCtCode(followUpItem.trim());
      return getSnomedCtCode(snomedData.conceptId, snomedData.term);
    })
  );

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Procedure",
      id,
      meta: procedureMetadata(),
      status,
      code: getSnomedCtCode(
        getSnomedDataType.conceptId,
        getSnomedDataType.term,
        notes
      ),
      subject: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        display: patientResource.resource.resourceType,
      },
      performedDateTime: performedDateTime,
      followUp: followUpData,
      text: procedureDiv(),
    },
  };
};
