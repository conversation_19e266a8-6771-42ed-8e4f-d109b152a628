import mongoose from "mongoose";
import { getExpiryByMinutes } from "../../utils/common.js";
import portalDb from "../../config/clinics.collections.config.js";
import {whatsapplogDb} from "../../config/whatsapp.collections.config.js";

import dotenv from "dotenv";
import axios from "axios";

dotenv.config();


export const getClinicCacheForWhatsapp = async(clinic) => {
        var staffModel = portalDb.model("Staff");
        var WhatsappCacheModel = whatsapplogDb.model("CacheWhatsappAppointmentDetail");

  var clinicSetting = await WhatsappCacheModel.findOne({ clinicId: clinic })
    .select({ timeSlots: 1, doctors: 1 })
    .exec();

        if (clinicSetting != null) {
            return clinicSetting;
        } else {
           

            const doctors = await staffModel.find({
                clinic: new mongoose
                    .Types
                    .ObjectId(clinic),
                    deleted: false,
                    isDoctor: true
                })
                .select({firstName: 1, lastName: 1, specialization: 1,availableTimeSlot:1})
                .exec();



            var whatsappCache = new WhatsappCacheModel({
                clinicId: clinic,
                doctors: doctors,
                expireAt: getExpiryByMinutes(1400) // oneday expiry
            });

    await whatsappCache.save();

            return {doctors: doctors};
        }
};


export const AddAppointmentOnScanAndShare = async (body,tokenNumber) => {
    try {
       
        const nameParts = body?.profile?.patient?.name.split(" ").filter(Boolean);

        const clinicPayload = {
            data: {
                name: `${nameParts[0]} ${nameParts[1]}`,
                firstName: `${nameParts[0]}`,
                lastName: `${nameParts[1]}`,
                gender: body?.profile?.patient?.gender === "F" ? "Female" : "Male",
                birthDate: new Date(
                    Date.UTC(
                        parseInt(body?.profile?.patient?.yearOfBirth),
                        parseInt(body?.profile?.patient?.monthOfBirth) - 1,
                        parseInt(body?.profile?.patient?.dayOfBirth)
                    )
                ),
                tokenNumber,
                appointmentDate: new Date(Date.now()),
                clientId: `${body?.metaData?.context}`,
                abhaNumber: `${body?.profile?.patient?.abhaNumber}`,
                abhaAddress: `${body?.profile?.patient?.abhaAddress}`,
                address:`${body?.profile?.patient?.address?.line}`,
                pincode: `${body?.profile?.patient?.address?.pincode}`,
                state: `${body?.profile?.patient?.address?.state}`,
                district: `${body?.profile?.patient?.address?.district}`,
                mobile: `${body?.profile?.patient?.phoneNumber}`,
                virtualConsultation: false,
                type: "SCAN_SHARE",
            },
        };

        const response = await axios.post(
            `${process.env.CLINIC_SERVER_URL}/booked-consultation/bookconsultationusingscanandshare`,
            clinicPayload,
            { headers: { "Content-Type": "application/json" } }
        );

        return response.data;
    } catch (error) {
        console.error("Error in callbackToClinic:", error.message);
        throw error;
    }


    
};

