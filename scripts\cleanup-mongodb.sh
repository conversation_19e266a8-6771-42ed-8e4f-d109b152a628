#!/bin/bash

# MongoDB FHIR Records Cleanup Script
# Usage: ./cleanup-mongodb.sh [days_to_keep] [dry_run]

set -e

# Configuration
DAYS_TO_KEEP=${1:-30}
DRY_RUN=${2:-true}
MONGO_URI=${MONGODB_URI:-"mongodb://localhost:27017/healtether"}

echo "🚀 MongoDB FHIR Records Cleanup Script"
echo "====================================="
echo "Days to keep: $DAYS_TO_KEEP"
echo "Dry run: $DRY_RUN"
echo "MongoDB URI: $MONGO_URI"
echo ""

# Calculate cutoff date (30 days ago)
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CUTOFF_DATE=$(date -v-${DAYS_TO_KEEP}d -u +"%Y-%m-%dT%H:%M:%S.000Z")
else
    # Linux
    CUTOFF_DATE=$(date -d "${DAYS_TO_KEEP} days ago" -u +"%Y-%m-%dT%H:%M:%S.000Z")
fi

echo "Cutoff date: $CUTOFF_DATE"
echo ""

# MongoDB JavaScript commands
MONGO_SCRIPT=$(cat << EOF
// Connect to database
print("📊 Current Database Statistics:");
print("==============================");

var stats = db.stats();
print("Total Database Size: " + (stats.dataSize / (1024 * 1024)).toFixed(2) + " MB");
print("Storage Size: " + (stats.storageSize / (1024 * 1024)).toFixed(2) + " MB");
print("Collections: " + stats.collections);
print("");

// FHIR Collections to clean
var collections = [
    "prescriptionfhirrecords",
    "healthdocumentfhirrecords", 
    "opconsultfhirrecords",
    "immunizationreportfhirrecords",
    "diagnosticreportfhirrecords",
    "dischargesummaryfhirrecords",
    "wellnessreportfhirrecords",
    "invoicereportfhirrecords"
];

var cutoffDate = new Date("$CUTOFF_DATE");
var totalDeleted = 0;
var dryRun = $DRY_RUN;

print("🧹 " + (dryRun ? "DRY RUN - " : "") + "Cleaning up records older than $DAYS_TO_KEEP days");
print("=".repeat(60));

collections.forEach(function(collectionName) {
    try {
        var collection = db.getCollection(collectionName);
        
        // Try different date field patterns
        var queries = [
            { "created.on": { \$lt: cutoffDate } },
            { "createdAt": { \$lt: cutoffDate } },
            { "created": { \$lt: cutoffDate } }
        ];
        
        var deleted = 0;
        
        for (var i = 0; i < queries.length; i++) {
            var query = queries[i];
            var count = collection.countDocuments(query);
            
            if (count > 0) {
                print("");
                print(collectionName + ":");
                print("  Found " + count + " old records");
                
                if (!dryRun) {
                    var result = collection.deleteMany(query);
                    deleted = result.deletedCount;
                    print("  ✅ Deleted " + deleted + " records");
                } else {
                    print("  🔍 Would delete " + count + " records (DRY RUN)");
                    deleted = count;
                }
                
                totalDeleted += deleted;
                break;
            }
        }
        
        if (deleted === 0) {
            print(collectionName + ": No old records found");
        }
        
    } catch (error) {
        print("Error processing " + collectionName + ": " + error.message);
    }
});

print("");
print("📈 Summary: " + (dryRun ? "Would delete " : "Deleted ") + totalDeleted + " total records");

if (!dryRun) {
    print("");
    print("📊 Database Statistics After Cleanup:");
    print("====================================");
    var newStats = db.stats();
    print("Total Database Size: " + (newStats.dataSize / (1024 * 1024)).toFixed(2) + " MB");
    print("Storage Size: " + (newStats.storageSize / (1024 * 1024)).toFixed(2) + " MB");
    print("Space Freed: " + ((stats.dataSize - newStats.dataSize) / (1024 * 1024)).toFixed(2) + " MB");
}
EOF
)

# Execute MongoDB script
echo "Executing cleanup script..."
echo "$MONGO_SCRIPT" | mongosh "$MONGO_URI" --quiet

echo ""
echo "✅ Script completed successfully"

# Usage instructions
if [ "$DRY_RUN" = "true" ]; then
    echo ""
    echo "💡 This was a dry run. To actually delete records, run:"
    echo "   ./cleanup-mongodb.sh $DAYS_TO_KEEP false"
fi
