import mongoose, { Schem<PERSON> } from "mongoose";

export const RecentchatSchema = new mongoose.Schema({
  timeStamp: {
    type: Date,
    default: Date.now,
  },
  mobile: {
    type: String,
    required: true,
    index: true,
    maxLength: 15
  },
  patientName:[{
      firstName:String,
      lastName:String,
      _id:false
  }],
  unReaded:{
    type:Boolean,
    default:true,
  },
  clinicId:{
    type:String,
    maxLength:45,
  },
  "expireAt":{
    type: Date,
    expires: 864000 
}
},{versionKey: '1.1'});
RecentchatSchema.index({ mobile: 1, clinicId: 1}, { unique: true });
//const RecentChatsModel = mongoose.model("WhatsappRecentChat",RecentchatSchema);
