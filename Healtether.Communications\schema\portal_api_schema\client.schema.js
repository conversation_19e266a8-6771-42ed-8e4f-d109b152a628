import mongoose from "mongoose";

export const ClientSchema = new mongoose.Schema(
  {
    clinicName: {
      type: String,
      required: true,
      maxLength: 255,
    },
    consultationCharge: {
      type: mongoose.Types.Decimal128,
    },
    phonepeSetting: {
      merchantId: {
        type: String,
        maxLength: 255,
      },
      saltKey: {
        type: String,
        maxLength: 255,
      },
      saltIndex: {
        type: String,
        maxLength: 1000,
      },
    },
    patientId: {
      prefix: String,
      suffix: String,
    },
    staffId: {
      prefix: String,
      suffix: String,
    },
    logo: {
      type: String,
      maxLength: 255,
    },

    isActive: {
      type: Boolean,
      default: true,
    },
    address: {
      type: String,
    },
    created: {
      on: {
        type: Date,
        default: Date.Now,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    modified: {
      on: {
        type: Date,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    isdeleted: {
      type: Boolean,
      default: false,
    },
    groupId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ClinicGroup",
    },
    adminUserId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  },
  { versionKey: "1.2" }
);
