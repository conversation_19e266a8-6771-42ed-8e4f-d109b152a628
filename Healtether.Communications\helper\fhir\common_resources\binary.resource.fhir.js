import { v4 as uuidv4 } from "uuid";
import { binaryMetadata } from "../../../utils/fhir.constants.js";

export const generateBinaryResource = async (binary) => {
    const id = uuidv4();

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Binary',
            id,
            meta: binaryMetadata(),
            contentType: binary?.contentType||"application/pdf",
            data: binary.data
        }
    }
}