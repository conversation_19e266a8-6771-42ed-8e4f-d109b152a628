#!/usr/bin/env node

import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// FHIR Record Schemas (simplified for cleanup)
const PrescriptionFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const HealthDocumentFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const OPConsultFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const ImmunizationFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const DiagnosticReportFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const DischargeSummaryFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const WellnessFHIRRecordSchema = new mongoose.Schema({}, { strict: false });
const InvoiceFHIRRecordSchema = new mongoose.Schema({}, { strict: false });

// Collection names mapping
const COLLECTIONS = {
  prescriptionfhirrecords: 'PrescriptionFHIRRecord',
  healthdocumentfhirrecords: 'HealthDocumentFHIRRecord',
  opconsultfhirrecords: 'OPConsultFHIRRecord',
  immunizationreportfhirrecords: 'ImmunizationReportFHIRRecord',
  diagnosticreportfhirrecords: 'DiagnosticReportFHIRRecord',
  dischargesummaryfhirrecords: 'DischargeSummaryFHIRRecord',
  wellnessreportfhirrecords: 'WellnessReportFHIRRecord',
  invoicereportfhirrecords: 'InvoiceReportFHIRRecord'
};

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/healtether';
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    return false;
  }
}

async function getCollectionStats() {
  console.log('\n📊 Current Database Statistics:');
  console.log('================================');
  
  try {
    const db = mongoose.connection.db;
    const stats = await db.stats();
    
    console.log(`Total Database Size: ${(stats.dataSize / (1024 * 1024)).toFixed(2)} MB`);
    console.log(`Storage Size: ${(stats.storageSize / (1024 * 1024)).toFixed(2)} MB`);
    console.log(`Index Size: ${(stats.indexSize / (1024 * 1024)).toFixed(2)} MB`);
    console.log(`Collections: ${stats.collections}`);
    
    console.log('\n📋 Collection Details:');
    console.log('----------------------');
    
    for (const [collectionName, modelName] of Object.entries(COLLECTIONS)) {
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments();
        const collStats = await db.command({ collStats: collectionName });
        const sizeMB = (collStats.size / (1024 * 1024)).toFixed(2);
        
        console.log(`${collectionName}: ${count} documents, ${sizeMB} MB`);
      } catch (error) {
        console.log(`${collectionName}: Collection not found or empty`);
      }
    }
  } catch (error) {
    console.error('Error getting database stats:', error.message);
  }
}

async function cleanupOldRecords(daysToKeep = 30, dryRun = true) {
  console.log(`\n🧹 ${dryRun ? 'DRY RUN - ' : ''}Cleaning up records older than ${daysToKeep} days`);
  console.log('='.repeat(60));
  
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
  console.log(`Cutoff date: ${cutoffDate.toISOString()}`);
  
  let totalDeleted = 0;
  
  for (const [collectionName, modelName] of Object.entries(COLLECTIONS)) {
    try {
      const collection = mongoose.connection.db.collection(collectionName);
      
      // Try different date field patterns
      const dateQueries = [
        { "created.on": { $lt: cutoffDate } },
        { "createdAt": { $lt: cutoffDate } },
        { "created": { $lt: cutoffDate } },
        { "_id": { $lt: mongoose.Types.ObjectId.createFromTime(cutoffDate.getTime() / 1000) } }
      ];
      
      let deleted = 0;
      
      for (const query of dateQueries) {
        const count = await collection.countDocuments(query);
        if (count > 0) {
          console.log(`\n${collectionName}:`);
          console.log(`  Found ${count} old records with query:`, JSON.stringify(query));
          
          if (!dryRun) {
            const result = await collection.deleteMany(query);
            deleted = result.deletedCount;
            console.log(`  ✅ Deleted ${deleted} records`);
          } else {
            console.log(`  🔍 Would delete ${count} records (DRY RUN)`);
            deleted = count;
          }
          
          totalDeleted += deleted;
          break; // Use the first query that finds records
        }
      }
      
      if (deleted === 0) {
        console.log(`${collectionName}: No old records found`);
      }
      
    } catch (error) {
      console.error(`Error processing ${collectionName}:`, error.message);
    }
  }
  
  console.log(`\n📈 Summary: ${dryRun ? 'Would delete' : 'Deleted'} ${totalDeleted} total records`);
  return totalDeleted;
}

async function cleanupAllRecords(dryRun = true) {
  console.log(`\n🗑️  ${dryRun ? 'DRY RUN - ' : ''}Cleaning up ALL FHIR records`);
  console.log('='.repeat(60));
  console.log('⚠️  WARNING: This will delete ALL FHIR records!');
  
  let totalDeleted = 0;
  
  for (const [collectionName, modelName] of Object.entries(COLLECTIONS)) {
    try {
      const collection = mongoose.connection.db.collection(collectionName);
      const count = await collection.countDocuments();
      
      if (count > 0) {
        console.log(`\n${collectionName}: ${count} records`);
        
        if (!dryRun) {
          const result = await collection.deleteMany({});
          console.log(`  ✅ Deleted ${result.deletedCount} records`);
          totalDeleted += result.deletedCount;
        } else {
          console.log(`  🔍 Would delete ${count} records (DRY RUN)`);
          totalDeleted += count;
        }
      } else {
        console.log(`${collectionName}: No records found`);
      }
      
    } catch (error) {
      console.error(`Error processing ${collectionName}:`, error.message);
    }
  }
  
  console.log(`\n📈 Summary: ${dryRun ? 'Would delete' : 'Deleted'} ${totalDeleted} total records`);
  return totalDeleted;
}

async function main() {
  console.log('🚀 MongoDB FHIR Records Cleanup Script');
  console.log('=====================================');
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const cleanAll = args.includes('--all');
  const daysToKeep = parseInt(args.find(arg => arg.startsWith('--days='))?.split('=')[1]) || 30;
  
  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode. Use --execute to actually delete records.');
  }
  
  // Connect to database
  const connected = await connectToDatabase();
  if (!connected) {
    process.exit(1);
  }
  
  // Show current stats
  await getCollectionStats();
  
  // Cleanup records
  if (cleanAll) {
    await cleanupAllRecords(dryRun);
  } else {
    await cleanupOldRecords(daysToKeep, dryRun);
  }
  
  // Show stats after cleanup
  if (!dryRun) {
    console.log('\n📊 Database Statistics After Cleanup:');
    await getCollectionStats();
  }
  
  // Close connection
  await mongoose.connection.close();
  console.log('\n✅ Script completed successfully');
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Show usage if help requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node cleanup-mongodb.js [options]

Options:
  --execute          Actually delete records (default: dry run)
  --all              Delete ALL FHIR records (dangerous!)
  --days=N           Keep records from last N days (default: 30)
  --help, -h         Show this help message

Examples:
  node cleanup-mongodb.js                    # Dry run, show what would be deleted
  node cleanup-mongodb.js --execute          # Delete records older than 30 days
  node cleanup-mongodb.js --days=7 --execute # Delete records older than 7 days
  node cleanup-mongodb.js --all --execute    # Delete ALL FHIR records (careful!)
`);
  process.exit(0);
}

// Run the script
main().catch(console.error);
