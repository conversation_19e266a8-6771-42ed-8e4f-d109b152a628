import { v4 as uuidv4 } from "uuid";
import {
  observationMetadata,
  observationCategory,
  observationDiv,
} from "../../../utils/fhir.constants.js";

export const generateObservationResource = async (
  observation,
  currentTime,
  patientResource,
  practitionerResource,
  doctorNames // doctorNames can be an array
) => {
  const id = uuidv4();

  // Ensure practitionerResource is an array
  const practitioners = Array.isArray(practitionerResource)
    ? practitionerResource
    : [];

  const normalize = (str) => (typeof str === "string" ? str.trim().toLowerCase() : "");

  // Ensure doctorNames is an array and normalize names
  const normalizedDoctorNames = Array.isArray(doctorNames)
    ? doctorNames.map(normalize)
    : [normalize(doctorNames)];

  // Find all matching practitioners
  const matchingPractitioners = practitioners.filter((practitioner) =>
    practitioner.resource.name?.some((nameObj) =>
      normalizedDoctorNames.includes(normalize(nameObj.text))
    )
  );

  // Special handling for Blood Pressure Observations
  if (observation.bloodPressure) {
    return {
      fullUrl: `urn:uuid:${id}`,
      resource: {
        resourceType: "Observation",
        id,
        meta: observationMetadata(),
        status: "final",
        category: [observationCategory()],
        code: {
          coding: [
            {
              system: "http://loinc.org",
              code: "85354-9",
              display: "Blood pressure panel with all children optional",
            },
          ],
          text: "Blood pressure panel with all children optional",
        },
        subject: {
          reference: `urn:uuid:${patientResource.resource.id}`,
          display: patientResource.resource.resourceType,
        },
        effectiveDateTime: observation.effectiveDateTime || currentTime,
        performer:
          matchingPractitioners.length > 0
            ? matchingPractitioners.map((practitioner) => ({
                reference: `urn:uuid:${practitioner.resource.id}`,
                display: practitioner.resource.resourceType,
              }))
            : undefined,
        component: [
          {
            code: {
              coding: [
                {
                  system: "http://loinc.org",
                  code: "8480-6",
                  display: "Systolic Blood Pressure",
                },
              ],
            },
            valueQuantity: {
              value: observation.bloodPressure[0].valueQuantity.value,
              unit: "mmHg",
              system: "http://unitsofmeasure.org",
              code: "mm[Hg]",
            },
            interpretation: [
              {
                coding: [
                  {
                    system:
                      "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
                    code: observation?.bloodPressure?.[0]?.interpretation?.charAt(0)?.toUpperCase() ?? '',
                    display: observation.bloodPressure[0].interpretation,
                  },
                ],
                text: observation.bloodPressure[0].interpretation,
              },
            ],
          },
          {
            code: {
              coding: [
                {
                  system: "http://loinc.org",
                  code: "8462-4",
                  display: "Diastolic Blood Pressure",
                },
              ],
            },
            valueQuantity: {
              value: observation.bloodPressure[0].valueQuantity.value,
              unit: "mmHg",
              system: "http://unitsofmeasure.org",
              code: "mm[Hg]",
            },
            interpretation: [
              {
                coding: [
                  {
                    system:
                      "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
                    code: observation?.bloodPressure?.[0]?.interpretation?.charAt(0)?.toUpperCase() ?? '',
                    display: observation.bloodPressure[0].interpretation,
                  },
                ],
                text: observation.bloodPressure[0].interpretation,
              },
            ],
          },
        ],
        // text: observationDiv(),
      },
    };
  }

  // For other vital sign observations (e.g., heart rate, temperature, etc.)
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Observation",
      id,
      meta: observationMetadata(),
      status: observation.status || "final",
      category: [observationCategory()],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: observation.code.code,
            display: observation.code.display,
          },
        ],
        text: observation.code.display,
      },
      subject: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        display: patientResource.resource.resourceType,
      },
      effectiveDateTime: observation.effectiveDateTime || currentTime,
      performer:
        matchingPractitioners.length > 0
          ? matchingPractitioners.map((practitioner) => ({
              reference: `urn:uuid:${practitioner.resource.id}`,
              display: practitioner.resource.resourceType,
            }))
          : undefined,
      valueQuantity: observation.valueQuantity
        ? {
            value: observation.valueQuantity.value,
            unit: observation.valueQuantity.unit,
            system: "http://unitsofmeasure.org",
            code: observation.valueQuantity.code,
          }
        : undefined
      // text: observationDiv(),
    },
  };
};
