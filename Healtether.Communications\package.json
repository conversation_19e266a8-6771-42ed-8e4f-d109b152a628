{"name": "healtether.external.communications", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Healtether healthcare", "license": "", "dependencies": {"@azure/communication-email": "^1.0.0", "@socket.io/mongo-adapter": "^0.3.2", "applicationinsights": "^2.9.6", "axios": "^1.6.1", "body-parser": "^1.20.2", "braces": "^3.0.3", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "express": "^4.18.2", "express-validator": "^7.2.0", "mongoose": "^8.0.0", "nodemon": "^3.0.1", "socket.io": "^4.7.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}}