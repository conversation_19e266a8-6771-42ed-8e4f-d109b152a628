import mongoose from "mongoose";
const StaffSchema = new mongoose.Schema({
    staffId:{
        type: String,
        maxLength: 255,
        required:true
    },
    firstName: {
        type: String,
        maxLength: 255,
        required:true
    },
    lastName: {
        type: String,
        maxLength: 255,
        required:true
    },
    specialization: {
        type: String,
        maxLength: 50
    },
    isDoctor: {
        type: Boolean,
        default:false
    },
    age: {
        type: Number,
        min: 1,
        max: 100
    },
    birthday: {
        type: Date
    },
    gender: {
        type: String,
        maxLength: 10
    },
    mobile: {
        type: String,
        maxLength: 15,
        required:true
    },
    countryCode:{
        type: String,
        default:'+91',
        maxLength: 10
      },
    email:  {
        type: String,
        maxLength: 100
    },
    address:  {
        house:{
            type: String,
            maxLength: 255
        },
        street:{
            type: String,
            maxLength: 1000 
        },
        landmarks:{
            type: String,
            maxLength: 1000
        },
        city:{
            type: String,
            maxLength: 500
        },
        pincode:{
            type: String,
            maxLength: 50
        }
    },
    documentType:  {
        type: String,
        maxLength: 100
    },
    documentNumber:  {
        type: String,
        maxLength: 100
    },
    upiId:  {
        type: String,   
        maxLength: 100
    },
    bankName:  {
        type: String,
        maxLength: 100
    },
    accountName:  {
        type: String,
        maxLength: 255
    },
    accountNo:  {
        type: String,
        maxLength: 100
    },
    ifsc: {
        type: String,
        maxLength: 50
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    modified: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    profilePic: String,
    documents: [{
        fileName:{
            type: String,
            maxLength: 255
        },
        blobName:{
            type: String,
            maxLength: 255
        },
        uploadedOn:Date
    }],
    deleted: {
        type: Boolean,
        default:false
    },
    availableTimeSlot:[
        {
            weekDay:[{
                type: String,
                maxLength: 5,
            }],
            timeSlot:[
                {
                    start:{
                        type: String,
                        maxLength: 15,
                    },
                    end:{
                        type: String,
                        maxLength: 15,
                    }
                }
            ],
            slotDuration:{
                type:Number
            }
        }
    ],
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    clinic:{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Client',
    }
},
{versionKey:'1.7'});

StaffSchema.index({ mobile: 1, clinic: 1}, { unique: true });
StaffSchema.index({ firstName: 1, lastName:1, clinic: 1}, { unique: true });
StaffSchema.index({ staffId:1, clinic: 1}, { unique: true });
// Create the user model
//const Staff = new mongoose.model("Staff", staffSchema);
export {StaffSchema};
