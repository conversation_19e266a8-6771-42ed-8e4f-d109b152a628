import axios from "services/axios/axios";
import store from "../../store/store";
import { GetJSONHederWithToken } from "../../utils/CommonMethods";

export const GetPaymentOverview = async (page, size, paramStr) => {
  try {
    const { currentClinic } = store.getState();
    var header = GetJSONHederWithToken();
    const response = await axios
      .get(
        "/payment/getpayments?clientId=" +
          currentClinic?.clinic?._id +
          "&page=" +
          page +
          "&size=" +
          size +
          "" +
          paramStr,
        header
      )
      .then((response) => {
        return response;
      });
    return response;
  } catch (error) {
    console.log(error);
  }
};

export const AddInvoice = async (id, patientId, clientId, data) => {
  try {
    var header = GetJSONHederWithToken();
    const formData = await axios
      .post(
        "/payment/addinvoice?id=" +
          id +
          "&patientId=" +
          patientId +
          "&clientId=" +
          clientId +
          "&staffName=" +
          clientId +
          "&staffId=" +
          clientId,
        data,
        header
      )
      .then((response) => {
        return response;
      });
    return formData;
  } catch (error) {
    console.log(error);
  }
};
export const SetCashPayment = async (data) => {
  try {
    console.log("PaymentService: SetCashPayment API call initiated", {
      data: data,
      timestamp: new Date().toISOString()
    });

    var header = GetJSONHederWithToken();
    const formData = await axios
      .post("/payment/setcashpayment", data, header)
      .then((response) => {
        console.log("PaymentService: SetCashPayment API response received", {
          status: response.status,
          data: response.data,
          timestamp: new Date().toISOString()
        });
        return response;
      });
    return formData;
  } catch (error) {
    console.error("PaymentService: SetCashPayment API error", {
      error: error.message,
      data: data,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

export const SendPaymentLink = async (data) => {
  try {
    console.log("PaymentService: SendPaymentLink API call initiated", {
      data: data,
      timestamp: new Date().toISOString()
    });

    var header = GetJSONHederWithToken();
    const formData = await axios
      .post("/payment/sendpaymentlink", data, header)
      .then((response) => {
        console.log("PaymentService: SendPaymentLink API response received", {
          status: response.status,
          data: response.data,
          timestamp: new Date().toISOString()
        });
        return response;
      });
    return formData;
  } catch (error) {
    console.error("PaymentService: SendPaymentLink API error", {
      error: error.message,
      data: data,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};


export const GetInvoiceById = async (invoiceId) => {
  try {
    var header = GetJSONHederWithToken();
    const invoice = await axios
      .get("/payment/getinvoicebyid?id=" + invoiceId, header)
      .then((response) => {
        return response;
      });
    return invoice;
  } catch (error) {
    console.log(error);
  }
};
