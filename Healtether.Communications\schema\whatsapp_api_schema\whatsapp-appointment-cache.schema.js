import mongoose from "mongoose";

export const WhatsappAppointmentSchema = new mongoose.Schema({
    "expireAt": {
        type: Date,
        expires: 10
    },
    clinicId: {
        type: String,
        index: true,
        unique: true,
        required: true
    },
    doctors: [
        {
            firstName: {
                type: String,
                maxLength: 255,
                required: true
            },
            lastName: {
                type: String,
                maxLength: 255,
                required: true
            },
            specialization: {
                type: String,
                maxLength: 50
            },
            availableTimeSlot:[
                {
                    weekDay:[{
                        type: String,
                        maxLength: 5,
                    }],
                    timeSlot:[
                        {
                            start:{
                                type: String,
                                maxLength: 15,
                            },
                            end:{
                                type: String,
                                maxLength: 15,
                            }
                        }
                    ],
                    slotDuration:{
                        type:Number
                    }
                }
            ]
        }
    ]
}, {versionKey: '1.2'});