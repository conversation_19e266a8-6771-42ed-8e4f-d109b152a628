import mongoose from "mongoose";
const PatientSchema =new mongoose.Schema({
    patientId:{
        type: String,
        index: true,
        maxLength: 255,
        required:true
    },
    firstName: {
        type: String,
        index: true,
        required:true
    },
    lastName: {
        type: String,
        index: true,
        required:true
    },
    age: {
        type: Number,
        min: 1,
        max: 100
    },
    height: {
        type: Number,
    },
    weight: {
        type: Number,
    },
    birthday: {
        type: Date
    },
    gender: {
        type: String
    },
    mobile: {
        type: String,
        maxLength: 15,
        required:true,
        index: true
    },
    whatsapp: {
        type: String
    },
    countryCode:{
        type: String,
        default:'+91',
        maxLength: 10
    },
    email: {
        type: String
    },
    address: {
        house:{
            type: String,
            maxLength: 255
        },
        street:{
            type: String,
            maxLength: 1000 
        },
        landmarks:{
            type: String,
            maxLength: 1000
        },
        city:{
            type: String,
            maxLength: 500
        },
        pincode:{
            type: String,
            maxLength: 50
        }
    },
    documentType: {
        type: String
    },
    documentNumber: {
        type: String
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    modified: {
        on: {
            type: Date
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    documents: [{
        fileName:{
            type: String,
            maxLength: 255
        },
        blobName:{
            type: String,
            maxLength: 255
        },
        uploadedOn:Date
    }],
  
    deleted: {
        type: Boolean,
        default:false
    },
    clinic:{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Client',
        index:true
    }
},
{versionKey:'1.4',toJSON:{virtuals: true},toObject: { virtuals: true }}
)

PatientSchema.virtual('appointments', {
    ref: 'Appointment',
    localField: '_id',
    foreignField: 'patientId'
  });
// Create the patient model
//const Patient = new mongoose.model("Patient", patientSchema);
export {PatientSchema};
