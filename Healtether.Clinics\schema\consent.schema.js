import mongoose from "mongoose";
const abhaConsentSchema = new mongoose.Schema(
  {
    patientId: {
      type: mongoose.Schema.Types.ObjectId,
      index: true,
    },
    clinicPatientId: {
      type: String,
      maxLength: 255,
    },
    clinicId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Client",
      index: true,
    },
    abhaId: {
      type: String,
      maxLength: 255,
    },
    abhaAddress: {
      type: String,
      maxLength: 255,
    },
    patientName: {
      type: String,
    },
    consentStatus: {
      type: String,
      enum: ["FAILED", "REQUESTED", "EXPIRED", "GRANTED"],
      maxLength: 20,
      default: "FAILED",
    },
    reason: {
      type: String,
    },
    purpose: {
      code: { type: String, required: true },
      text: { type: String, required: true },
      refUri: { type: String },
    },
    hiuId: {
      type: String,
    },
    consentRequestId: {
      type: String,
    },
    careContexts: [
      {
        patientReference: { type: String },
        careContextReference: { type: String },
      },
    ],
    consentArtefacts: [
      {
        id: {
          type: String,
        },
        hiTypes: [{ type: String }],
        requestId: { type: String },
        privateKey: { type: String },
        nonce: { type: String },
        transactionId: { type: String },
        purpose: {
          code: { type: String },
          text: { type: String },
          refUri: { type: String },
        },
        permission: {
          dateRange: {
            from: { type: Date },
            to: { type: Date },
          },
          frequency: {
            unit: { type: String },
            value: { type: Number },
            repeats: { type: Number },
          },
          accessMode: { type: String },
          dataEraseAt: { type: Date },
        },
      },
    ],
    hiTypes: [{ type: String }],
    requester: {
      name: { type: String, required: true },
      identifier: {
        type: { type: String },
        value: { type: String },
        system: { type: String },
      },
    },
    requestId: {
      type: String,
      maxLength: 255,
      index: true,
    },
    consentArtifactId: {
      type: String,
      maxLength: 255,
      index: true,
    },
    permission: {
      dateRange: {
        from: { type: Date, required: true },
        to: { type: Date, required: true },
      },
      frequency: {
        unit: { type: String },
        value: { type: Number },
        repeats: { type: Number },
      },
      accessMode: { type: String },
      dataEraseAt: { type: Date },
    },
    requestedToAbha: {
      type: Boolean,
      default: false,
    },
    consentStatusUpdatedOn:{
      type: Date,
      default: null,
    },
    grantedConsentData: [String]
  
  },
 
  { timestamps: true },
  { versionKey: "1.0" }
);

abhaConsentSchema.index({ patientId: 1, clinicId: 1 });
abhaConsentSchema.index({ requestId: 1 });

const AbhaConsentsModel = mongoose.model("abhaConsents", abhaConsentSchema);

// Export the model for use in other files
export { AbhaConsentsModel };
