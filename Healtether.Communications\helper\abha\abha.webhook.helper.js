import mongoose from "mongoose";
import { PROFILE_ON_SHARE } from "../../utils/abha.api.js";
import { AddAppointmentOnScanAndShare } from "../clinic/clinic.helper.js";
import { ackProfileShare } from "./abdm.helper.js";
import portalDb from "../../config/clinics.collections.config.js";
import moment from "moment";
const tokenModel = portalDb.model("Token");
export const profileOnShare = async (req) => {
    const body = req.body;
    let data={
      appointmentDate: new Date().toISOString(),
      clientId:body?.metaData?.context,
    }
   const tokenNumber = await generateToken(data);

  const headers = { ...req.headers };

  if (!body) {
    return {
      isSuccess: false,
      data: { error: { message: "Invalid request body" } },
    };
  }

  const payload = {
    acknowledgement: {
      abhaAddress: body.profile.patient.abhaAddress,
      status: "SUCCESS",
      profile: {
        context: body.metaData.context,
        tokenNumber: tokenNumber,
        expiry: 1800,
      },
    },
    response: {
      requestId: headers["request-id"],
    },
  };

  const result = await ackProfileShare(PROFILE_ON_SHARE, req, payload);
  // console.log("result",await result.json());
  console.log("result", await result);

  if (result.status === 202 && result) {
    const clinicResponse = await AddAppointmentOnScanAndShare(body,tokenNumber);
    console.log("Appointment created successfully in Clinic:", clinicResponse);

    return clinicResponse;
  } else {
    return {
      isSuccess: false,
      data: {
        error: { message: "Failed to get a valid result from ABDM Gateway" },
      },
    };
  }
};

  export const generateToken = async (data) => {
    let tokenNumber;
  
    const appointmentDate = data.appointmentDate;
  
      const currentTokenNumber = await getCurrentTokenNumber(
        data.clientId,
        appointmentDate
      );
        tokenNumber = await getNextTokenNumber(data.clientId, appointmentDate);
  
      await tokenModel.findOneAndUpdate(
        {
          clinic: data.clientId,
          date: moment(appointmentDate).startOf("day").toDate(),
        },
        { value: Math.max(tokenNumber, currentTokenNumber) },
        { upsert: true }
      );
    
  
    return tokenNumber;
  };

  export const getNextTokenNumber = async (clinicId, appointmentDate) => {
    let newmongooseclinicId = clinicId && new mongoose.Types.ObjectId(clinicId);
    const dateOnly = moment(appointmentDate).startOf("day").toDate();
  
    const counter = await tokenModel.findOneAndUpdate(
      {
        clinic: new mongoose.Types.ObjectId(newmongooseclinicId),
        date: dateOnly,
      },
      { $inc: { value: 1 } },
      { new: true, upsert: true }
    );
  
    return counter.value;
  };
  
  export const getCurrentTokenNumber = async (clinicId, appointmentDate) => {
    const dateOnly = moment(appointmentDate).startOf("day").toDate();
    const counter = await tokenModel.findOne({
      clinic: clinicId,
      date: dateOnly,
    });
    return counter ? counter.value : 0;
  };