import mongoose from "mongoose";
import {
  ClientAutoIdentifier,
  Appointment,
  Payment,
  Invoice,
} from "../../model/clinics.model.js";
import {
  calculateAmountAfterDisc,
  getFileTimeNow,
  getDecimal,
} from "../../utils/common.utils.js";
import path from "path";
import { LogMessageInCloudWatch } from "../../config/cloudWatchLogger.js";
import { setPaymentStatus } from "../appointment/appointment.helper.js";
import { createHash } from "crypto";

export const overview = async (
  clientId,
  pg,
  size,
  keyword,
  sortby,
  direction
) => {
  const regex = new RegExp(keyword, "i"); // i for case insensitive

  const findObj =
    keyword != null
      ? {
          $or: [
            {
              mobile: {
                $regex: regex,
              },
            },
            {
              name: {
                $regex: regex,
              },
            },
          ],
          //   isDeleted: false,
          clinic: new mongoose.Types.ObjectId(clientId),
        }
      : {
          //   isDeleted: false,
          "ended.yes": true,
          clinic: new mongoose.Types.ObjectId(clientId),
        };
  const sortObj =
    sortby != null
      ? {
          sortby: direction == "desc" ? -1 : 1,
        }
      : {
          created: -1,
        };

  const paymentCollection = await Appointment.find(findObj)
    .populate({
      path: "invoicedetail",
      perDocumentLimit: 1,
      select: {
        totalAmount: 1,
        paidAmount: 1,
        totalCost: 1,
      },
    })
    .sort(sortObj)
    .skip((pg - 1) * size)
    .limit(size)
    .select({
      _id: 1,
      name: 1,
      mobile: 1,
      virtualConsultation: 1,
      appointmentDate: 1,
      timeSlot: 1,
      doctorName: 1,
      patientId: 1,
      paymentStatus: 1,
      invoicedetail: 1,
    })
    .exec();

  const paymentCount = await Appointment.find(findObj).count();
  return { data: paymentCollection, totalCount: paymentCount };
};
export default async function updateInvoice(
  id,
  treatments,
  clinicId,
  discountRate,
  staffId,
  staffName
) {
  var totalAmt = 0;
  var totalTax = 0;
  discountRate = discountRate == "" ? 0 : discountRate;

  for (var i = 0; i < treatments.length; i++) {
    // Calculate amount after discount for each treatment
    var amountAfterDisc = calculateAmountAfterDisc(
      treatments[i].quantity,
      treatments[i].amount,
      treatments[i].discRate
    );

    // Calculate tax for each item based on SGST and CGST
    var sgstAmount =
      parseFloat(amountAfterDisc) * (parseFloat(treatments[i].sgst || 0) / 100);
    var cgstAmount =
      parseFloat(amountAfterDisc) * (parseFloat(treatments[i].cgst || 0) / 100);
    var itemTaxAmount = sgstAmount + cgstAmount;

    // Store the tax amount for each treatment
    treatments[i].taxAmount = itemTaxAmount;

    // Add to totals
    totalAmt = parseFloat(totalAmt) + parseFloat(amountAfterDisc);
    totalTax = parseFloat(totalTax) + parseFloat(itemTaxAmount);
  }

  // Calculate final discount
  var discount =
    Math.round(parseFloat(totalAmt) * (parseFloat(discountRate) / 100)) || 0;

  // Calculate final cost
  var totalCost =
    parseFloat(totalAmt) + parseFloat(totalTax) - parseFloat(discount);

  var invoiceCollection = await Invoice.findById(id).exec();
  if (invoiceCollection != null) {
    invoiceCollection.treatments = treatments?.map((x) => {
      const amountAfterDisc = calculateAmountAfterDisc(
        x.quantity,
        x.amount,
        x.discRate
      );
      const sgstAmount =
        parseFloat(amountAfterDisc) * (parseFloat(x.sgst || 0) / 100);
      const cgstAmount =
        parseFloat(amountAfterDisc) * (parseFloat(x.cgst || 0) / 100);
      return {
        treatment: x.treatment,
        quantity: x.quantity,
        amount: new mongoose.Types.Decimal128(x.amount?.toString()),
        discRate: new mongoose.Types.Decimal128(x.discRate?.toString()),
        sgstRate: new mongoose.Types.Decimal128(x.sgst?.toString() || "0"),
        cgstRate: new mongoose.Types.Decimal128(x.cgst?.toString() || "0"),
        cgstAmount: new mongoose.Types.Decimal128(cgstAmount.toString() || "0"),
        sgstAmount: new mongoose.Types.Decimal128(sgstAmount.toString() || "0"),
        taxAmount: new mongoose.Types.Decimal128(
          x.taxAmount?.toString() || "0"
        ),
      };
    });
    invoiceCollection.totalAmount = new mongoose.Types.Decimal128(
      totalAmt.toString()
    );
    invoiceCollection.discountRate = discountRate;
    invoiceCollection.discount = new mongoose.Types.Decimal128(
      discount.toString()
    );
    invoiceCollection.totalTax = new mongoose.Types.Decimal128(
      totalTax.toString()
    );
    invoiceCollection.totalCost = new mongoose.Types.Decimal128(
      totalCost.toString()
    );
    invoiceCollection.modified = {
      on: new Date(),
      by: {
        id: staffId,
        name: staffName,
      },
    };
    invoiceCollection.save();
  } else {
    const invoiceCollection = new Invoice({
      client: clinicId,
      treatments: treatments?.map((x) => {
        const amountAfterDisc = calculateAmountAfterDisc(
          x.quantity,
          x.amount,
          x.discRate
        );
        const sgstAmount =
          parseFloat(amountAfterDisc) * (parseFloat(x.sgst || 0) / 100);
        const cgstAmount =
          parseFloat(amountAfterDisc) * (parseFloat(x.cgst || 0) / 100);
        return {
          treatment: x.treatment,
          quantity: x.quantity,
          amount: new mongoose.Types.Decimal128(x.amount?.toString()),
          discRate: new mongoose.Types.Decimal128(x.discRate?.toString()),
          sgstRate: new mongoose.Types.Decimal128(x.sgst?.toString() || "0"),
          cgstRate: new mongoose.Types.Decimal128(x.cgst?.toString() || "0"),
          cgstAmount: new mongoose.Types.Decimal128(
            cgstAmount.toString() || "0"
          ),
          sgstAmount: new mongoose.Types.Decimal128(
            sgstAmount.toString() || "0"
          ),
          taxAmount: new mongoose.Types.Decimal128(
            x.taxAmount?.toString() || "0"
          ),
        };
      }),
      totalAmount: new mongoose.Types.Decimal128(totalAmt.toString()),
      discountRate: discountRate,
      discount: new mongoose.Types.Decimal128(discount.toString()),
      totalTax: new mongoose.Types.Decimal128(totalTax.toString()),
      totalCost: new mongoose.Types.Decimal128(totalCost.toString()),
      modified: {
        on: new Date(),
        by: {
          id: staffId,
          name: staffName,
        },
      },
      _id: id,
    });
    var upsertData = invoiceCollection.toObject();
    await Invoice.findOneAndUpdate(
      {
        _id: upsertData._id,
      },
      upsertData
    );
  }
  return true;
}
export async function createInvoiceWithConsultationCharge(
  staffId,
  staffName,
  appointmentId,
  clinicId
) {
  var currentClinicId = await ClientAutoIdentifier.findOne({ clinic: clinicId })
    .populate({
      path: "clinic",
      perDocumentLimit: 1,
      select: {
        consultationCharge: 1,
      },
    })
    .exec();

  var invoiceNo =
    (currentClinicId?.currentInvoiceId != null
      ? currentClinicId?.currentInvoiceId
      : 0) + 1;
  var consultationCharge = currentClinicId?.clinic?.consultationCharge;
  const invoiceCollection = new Invoice({
    invoiceNumber: invoiceNo,
    clinic: clinicId,
    treatments: [
      {
        treatment: "consultation",
        quantity: 1,
        amount: consultationCharge ?? 0,
        discRate: 0,
      },
    ],
    totalAmount: consultationCharge ?? 0,
    discountRate: 0,
    discount: 0,
    totalTax: 0,
    totalCost: consultationCharge ?? 0,
    created: {
      on: new Date(),
      by: {
        id: staffId,
        name: staffName,
      },
    },
    appointmentId: appointmentId,
  });
  var upsertData = invoiceCollection.toObject();
  await Invoice.findOneAndUpdate(
    {
      _id: upsertData._id,
    },
    upsertData,
    { upsert: true }
  );

  await ClientAutoIdentifier.findOneAndUpdate(
    {
      clinic: clinicId,
    },
    {
      currentInvoiceId: invoiceNo,
    },
    { upsert: true }
  );
  return upsertData;
}

export async function invoiceById(id) {
  const invoice = await Invoice.findById(id)
    .populate({
      path: "appointmentId",
      perDocumentLimit: 1,
      populate: {
        path: "patientId",
        perDocumentLimit: 1,
      },
    })
    .populate({
      path: "clinic",
      perDocumentLimit: 1,
    })
    .select({
      totalAmount: 1,
      totalTax: 1,
      totalCost: 1,
      paidAmount: 1,
      discountRate: 1,
      discount: 1,
      completed: 1,
      treatments: 1,
      invoiceNumber: 1,
      clinic: 1,
      created: 1,
    })
    .exec();

  return invoice;
}

export async function addPaymentByCash(
  invoiceId,
  user,
  clientId,
  amount,
  paymentMode
) {
  const invoice = await Invoice.findById(invoiceId).exec();

  if (amount == null) {
    amount = getDecimal(invoice.paidAmount) - getDecimal(invoice.totalAmount);
  }
  var afterPaid = getDecimal(invoice.paidAmount) + getDecimal(amount);

  if (getDecimal(invoice.totalCost) < afterPaid) {
    return "Invalid Amount";
  }

  invoice.paidAmount = new mongoose.Types.Decimal128(afterPaid.toString());
  invoice.modified = {
    on: new Date(),
    by: user,
  };
  await invoice.save();
  await addPayment(
    invoice?._id,
    invoice?.totalAmount,
    amount,
    clientId,
    false,
    user,
    paymentMode
  );

  if (
    invoice.appointmentId != undefined &&
    !(getDecimal(invoice.totalAmount) - parseFloat(afterPaid) > 0)
  ) {
    await setPaymentStatus(invoice.appointmentId, true);
  }

  return invoice;
}

async function addPayment(
  invoiceId,
  totalAmount,
  paidAmount,
  clinicId,
  isOnline,
  user,
  paymentMode
) {
  const paymentCollection = new Payment({
    clinic: clinicId,
    transactionId: getFileTimeNow().toString(),
    transactionAmount: new mongoose.Types.Decimal128(totalAmount?.toString()),
    paidOn: !isOnline ? new Date().toISOString() : null,
    amountPaid: new mongoose.Types.Decimal128(paidAmount?.toString()),
    isSuccess: !isOnline,
    created: {
      on: new Date().toISOString(),
      by: user,
    },
    isOnline: isOnline,
    invoice: invoiceId,
    paymentMode: paymentMode,
  });

  return await paymentCollection.save();
}

export const createPaymentLink = async (invoiceWithClinicDetail, user) => {
  if (invoiceWithClinicDetail == null) return false;

  let amountToPay = calculateAmount(
    invoiceWithClinicDetail?.totalAmount,
    invoiceWithClinicDetail?.paidAmount
  );
  let patientMobile = invoiceWithClinicDetail?.appointmentId?.patientId?.mobile;
  let phonepeSetting = invoiceWithClinicDetail?.clinic?.phonepeSetting;

  let paymentInfo = await addPayment(
    invoiceWithClinicDetail?._id,
    amountToPay,
    0,
    invoiceWithClinicDetail?.clinic?._id,
    true,
    user
  );
  if (paymentInfo?._id != null) {
    const salt = phonepeSetting?.saltKey; //"3b607857-098e-4597-a08b-85e4b8a8e209";
    const saltIndex = phonepeSetting?.saltIndex;
    const payload = JSON.stringify({
      merchantId: phonepeSetting?.merchantId, // "HEALTETHERONLINE",
      merchantTransactionId: paymentInfo?._id,
      merchantUserId: invoiceWithClinicDetail?.appointmentId?.patientId?._id,
      amount: amountInPaisa(amountToPay),
      redirectUrl: process.env.PHONEPE_REDIRECT,
      redirectMode: "REDIRECT",
      callbackUrl: process.env.PHONEPE_CALLBACK,
      mobileNumber: patientMobile,
      paymentInstrument: {
        type: "PAY_PAGE",
      },
    });
    const base64payload = Buffer.from(payload, "utf8").toString("base64");

    const hash = createHash("sha256")
      .update(base64payload + "/pg/v1/pay" + salt)
      .digest("hex");

    const finalHash = hash + `###${saltIndex}`;

    // Use fetch with async/await
    const response = await fetch(process.env.PHONEPE_CREATE_LINK, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Verify": finalHash,
      },
      body: JSON.stringify({ request: base64payload }),
    });

    // Await the JSON response
    const json = await response.json();

    // console.log(json);
    return json;
  }
};

function calculateAmount(totalAmount, paidAmount) {
  let totalInFloat = getDecimal(totalAmount ?? 0);
  let paidInFloat = getDecimal(paidAmount ?? 0);
  if (paidInFloat > totalInFloat) {
    return 0;
  }
  return totalInFloat - paidAmount;
}

const amountInPaisa = (amt) => getDecimal(amt) * 100;

export const phonePeCallback = async (reqBody, xVerify) => {
  const decodedResponse = JSON.parse(
    Buffer.from(reqBody.response, "base64").toString("utf-8")
  );
  const {
    success,
    code,
    data: {
      merchantTransactionId,
      amount,
      state,
      transactionId,
      paymentInstrument,
    },
  } = decodedResponse;

  //  Find the payment record
  const payment = await Payment.findOne({
    _id: merchantTransactionId,
  })
    .populate({
      path: "clinic",
      perDocumentLimit: 1,
      select: {
        phonepeSetting: 1,
      },
    })
    .populate({
      path: "invoice",
      perDocumentLimit: 1,
    });

  if (payment == null) {
    return false;
  }
  //  Verify the checksum
  const calculatedChecksum = verifyChecksum(
    reqBody.response,
    payment?.clinic?.phonepeSetting,
    xVerify
  );
  if (!calculatedChecksum) {
    return false;
  }

  //  Determine payment success
  const isPaymentSuccessful =
    success &&
    (state === "COMPLETED" || state === "SUCCESS") &&
    code === "PAYMENT_SUCCESS";
  if (isPaymentSuccessful) {
    payment.paidOn = new Date().toISOString();
    payment.amountPaid = mongoose.Types.Decimal128.fromString(
      (amount / 100).toFixed(2)
    );
    payment.referenceId = transactionId;
  }
  payment.payload = JSON.stringify(decodedResponse.data);
  payment.responsed = true;
  payment.isSuccess = isPaymentSuccessful;
  await payment.save();

  var invoice = payment?.invoice;
  if (invoice != null && isPaymentSuccessful) {
    var afterPaid = getDecimal(invoice?.paidAmount) + getDecimal(amount) / 100;
    invoice.paidAmount = new mongoose.Types.Decimal128(afterPaid.toString());
    await invoice.save();

    if (
      invoice.appointmentId != undefined &&
      !(getDecimal(invoice.totalAmount) - parseFloat(afterPaid) > 0)
    ) {
      await setPaymentStatus(invoice.appointmentId, true);
    }
  }
  return true;
};

function verifyChecksum(response, phonepeSetting, xVerify) {
  const responseString = response;
  const hashString = responseString + phonepeSetting.saltKey;
  const hash = createHash("sha256").update(hashString).digest("hex");
  const expectedXVerify = `${hash}###${phonepeSetting.saltIndex}`;
  return xVerify === expectedXVerify;
}
