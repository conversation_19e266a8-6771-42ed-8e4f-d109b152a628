import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import bodyParser from "body-parser";
import mongoose from "mongoose";
import userRouter from "./routes/userroute.js";
import messageRouter from "./routes/messageroute.js";
import whatsappChatRouter from "./routes/whatsappchatroute.js";
import { SocketConnection } from "./config/socket-io.js";
import { createServer } from "http";
import errorHandler from "./middleware/globalErrorHandler.js";
import { initializeAppInsights } from "./config/appinsight.config.js";
import abhaRouter from "./routes/abha.route.js";
import { swaggerSpec, swaggerUi } from "./config/swagger.config.js";

mongoose.set("strictQuery", true);

const app = express();
initializeAppInsights();
//const bodyparser = require("body-parser");
dotenv.config();
const port = process.env.PORT;

// Configure body parsing with increased limits
app.use(express.json({
  limit: '100mb',
  verify: (_req, _res, buf, _encoding) => {
    // This helps with debugging payload size issues
    if (buf && buf.length >= 100 * 1024 * 1024) {
      console.warn(`Large payload detected: ${buf.length} bytes`);
    }
  }
}));
app.use(express.urlencoded({ limit: '100mb', extended: true }));
app.use(bodyParser.urlencoded({ limit: '100mb', extended: true }));
app.use(
  cors({
    origin: process.env.CORS_URL,
    methods: ["GET", "POST", "PUT", "PATCH", "DELETE"],
    credentials: true,
  })
);

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
// Add specific error handler for payload size errors
app.use((error, _req, res, next) => {
  if (error.type === 'entity.too.large') {
    return res.status(413).json({
      error: 'Payload too large',
      message: 'Request entity too large. Maximum allowed size is 100MB.',
      limit: '100MB'
    });
  }
  next(error);
});

app.use("/api/user", userRouter);
app.use("/api/message", messageRouter);
app.use("/api/whatsappchat", whatsappChatRouter);
app.use("/api/abha",abhaRouter);
app.use(errorHandler);

const httpServer = createServer(app);
SocketConnection(httpServer);
httpServer.listen(port, () => {
  console.log(`Server is running on port ` + port);
});
process.on('uncaughtException', (error) => {
  console.log('test Uncaught Exception:', error);

  // Optional: Perform additional error handling, e.g., sending an error report or shutting down the process.
});